import sqlite3
from datetime import datetime

class Diagnosis:
    """نموذج التشخيصات"""
    
    def __init__(self, db_manager):
        self.db = db_manager
    
    def add_diagnosis(self, name, description=""):
        """إضافة تشخيص جديد أو زيادة عداد الاستخدام"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # التحقق من وجود التشخيص
            cursor.execute("SELECT id, usage_count FROM diagnoses WHERE name = ?", (name,))
            existing = cursor.fetchone()
            
            if existing:
                # زيادة عداد الاستخدام
                new_count = existing[1] + 1
                cursor.execute(
                    "UPDATE diagnoses SET usage_count = ? WHERE id = ?",
                    (new_count, existing[0])
                )
                diagnosis_id = existing[0]
            else:
                # إضافة تشخيص جديد
                cursor.execute(
                    "INSERT INTO diagnoses (name, description, usage_count) VALUES (?, ?, 1)",
                    (name, description)
                )
                diagnosis_id = cursor.lastrowid
            
            conn.commit()
            conn.close()
            return diagnosis_id
            
        except sqlite3.Error as e:
            print(f"خطأ في إضافة التشخيص: {e}")
            return None
    
    def get_all_diagnoses(self):
        """الحصول على جميع التشخيصات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, description, usage_count, created_at
                FROM diagnoses
                ORDER BY usage_count DESC, name ASC
            """)
            
            diagnoses = []
            for row in cursor.fetchall():
                diagnoses.append({
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'usage_count': row[3],
                    'created_at': row[4]
                })
            
            conn.close()
            return diagnoses
            
        except sqlite3.Error as e:
            print(f"خطأ في جلب التشخيصات: {e}")
            return []
    
    def get_popular_diagnoses(self, limit=10):
        """الحصول على التشخيصات الأكثر استخداماً"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, description, usage_count
                FROM diagnoses
                WHERE usage_count > 0
                ORDER BY usage_count DESC, name ASC
                LIMIT ?
            """, (limit,))
            
            diagnoses = []
            for row in cursor.fetchall():
                diagnoses.append({
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'usage_count': row[3]
                })
            
            conn.close()
            return diagnoses
            
        except sqlite3.Error as e:
            print(f"خطأ في جلب التشخيصات الشائعة: {e}")
            return []
    
    def search_diagnoses(self, search_term, limit=20):
        """البحث في التشخيصات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            search_pattern = f"%{search_term}%"
            cursor.execute("""
                SELECT id, name, description, usage_count
                FROM diagnoses
                WHERE name LIKE ? OR description LIKE ?
                ORDER BY usage_count DESC, name ASC
                LIMIT ?
            """, (search_pattern, search_pattern, limit))
            
            diagnoses = []
            for row in cursor.fetchall():
                diagnoses.append({
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'usage_count': row[3]
                })
            
            conn.close()
            return diagnoses
            
        except sqlite3.Error as e:
            print(f"خطأ في البحث في التشخيصات: {e}")
            return []
    
    def get_all_diagnosis_names(self):
        """الحصول على أسماء جميع التشخيصات للاقتراحات التلقائية"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT name FROM diagnoses
                ORDER BY usage_count DESC, name ASC
            """)
            
            names = [row[0] for row in cursor.fetchall()]
            conn.close()
            return names
            
        except sqlite3.Error as e:
            print(f"خطأ في جلب أسماء التشخيصات: {e}")
            return []
    
    def update_diagnosis(self, diagnosis_id, name, description=""):
        """تحديث تشخيص"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE diagnoses 
                SET name = ?, description = ?
                WHERE id = ?
            """, (name, description, diagnosis_id))
            
            success = cursor.rowcount > 0
            conn.commit()
            conn.close()
            return success
            
        except sqlite3.Error as e:
            print(f"خطأ في تحديث التشخيص: {e}")
            return False
    
    def delete_diagnosis(self, diagnosis_id):
        """حذف تشخيص"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM diagnoses WHERE id = ?", (diagnosis_id,))
            
            success = cursor.rowcount > 0
            conn.commit()
            conn.close()
            return success
            
        except sqlite3.Error as e:
            print(f"خطأ في حذف التشخيص: {e}")
            return False
    
    def get_diagnosis_by_id(self, diagnosis_id):
        """الحصول على تشخيص بالمعرف"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, description, usage_count, created_at
                FROM diagnoses
                WHERE id = ?
            """, (diagnosis_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return {
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'usage_count': row[3],
                    'created_at': row[4]
                }
            return None
            
        except sqlite3.Error as e:
            print(f"خطأ في جلب التشخيص: {e}")
            return None
    
    def get_diagnosis_statistics(self):
        """الحصول على إحصائيات التشخيصات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # إجمالي التشخيصات
            cursor.execute("SELECT COUNT(*) FROM diagnoses")
            total_diagnoses = cursor.fetchone()[0]
            
            # إجمالي الاستخدامات
            cursor.execute("SELECT SUM(usage_count) FROM diagnoses")
            total_usage = cursor.fetchone()[0] or 0
            
            # أكثر تشخيص استخداماً
            cursor.execute("""
                SELECT name, usage_count FROM diagnoses
                ORDER BY usage_count DESC LIMIT 1
            """)
            top_diagnosis = cursor.fetchone()
            
            conn.close()
            
            return {
                'total_diagnoses': total_diagnoses,
                'total_usage': total_usage,
                'top_diagnosis': {
                    'name': top_diagnosis[0] if top_diagnosis else '',
                    'count': top_diagnosis[1] if top_diagnosis else 0
                }
            }
            
        except sqlite3.Error as e:
            print(f"خطأ في جلب إحصائيات التشخيصات: {e}")
            return {
                'total_diagnoses': 0,
                'total_usage': 0,
                'top_diagnosis': {'name': '', 'count': 0}
            }
    
    def add_default_diagnoses(self):
        """إضافة تشخيصات افتراضية شائعة"""
        default_diagnoses = [
            ("نزلة برد", "التهاب في الجهاز التنفسي العلوي"),
            ("صداع", "ألم في الرأس"),
            ("التهاب الحلق", "التهاب في منطقة الحلق"),
            ("حمى", "ارتفاع في درجة حرارة الجسم"),
            ("آلام المعدة", "ألم في منطقة البطن"),
            ("التهاب المفاصل", "التهاب في المفاصل"),
            ("ضغط الدم المرتفع", "ارتفاع في ضغط الدم"),
            ("السكري", "مرض السكري"),
            ("الربو", "مرض في الجهاز التنفسي"),
            ("الأنيميا", "فقر الدم"),
            ("التهاب الأذن", "التهاب في الأذن"),
            ("الأرق", "صعوبة في النوم"),
            ("القلق", "اضطراب القلق"),
            ("الاكتئاب", "اضطراب الاكتئاب"),
            ("حساسية", "رد فعل تحسسي")
        ]
        
        for name, description in default_diagnoses:
            self.add_diagnosis(name, description)
