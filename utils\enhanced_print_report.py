"""
تقرير طباعة محسن مع إعدادات تخصيص كاملة
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QGroupBox, QGridLayout,
                            QComboBox, QSpinBox, QCheckBox, QMessageBox,
                            QTabWidget, QWidget, QScrollArea)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QTextDocument
from datetime import datetime
import json
import os

try:
    from PyQt6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
    PRINT_SUPPORT_AVAILABLE = True
except ImportError:
    PRINT_SUPPORT_AVAILABLE = False

class EnhancedPrintReportDialog(QDialog):
    """نافذة تقرير طباعة محسنة"""
    
    def __init__(self, patient_data, visit_data, treatment_text, parent=None):
        super().__init__(parent)
        self.patient_data = patient_data
        self.visit_data = visit_data
        self.treatment_text = treatment_text
        
        self.setWindowTitle("📋 تقرير طباعة محسن")
        self.setFixedSize(1000, 800)
        self.setModal(True)
        
        # تحميل الإعدادات
        self.settings = self.load_settings()
        
        self.init_ui()
        self.update_preview()
        
    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # التبويبات
        tab_widget = QTabWidget()
        
        # تبويب المعاينة
        preview_tab = self.create_preview_tab()
        tab_widget.addTab(preview_tab, "👁️ المعاينة")
        
        # تبويب الإعدادات السريعة
        settings_tab = self.create_quick_settings_tab()
        tab_widget.addTab(settings_tab, "⚙️ إعدادات سريعة")
        
        layout.addWidget(tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        # زر الإعدادات المتقدمة
        advanced_button = QPushButton("🔧 إعدادات متقدمة")
        advanced_button.setFixedHeight(40)
        advanced_button.clicked.connect(self.open_advanced_settings)
        buttons_layout.addWidget(advanced_button)
        
        # زر الطباعة
        print_button = QPushButton("🖨️ طباعة")
        print_button.setFixedHeight(40)
        print_button.clicked.connect(self.print_report)
        buttons_layout.addWidget(print_button)
        
        # زر حفظ PDF
        pdf_button = QPushButton("💾 حفظ PDF")
        pdf_button.setFixedHeight(40)
        pdf_button.clicked.connect(self.save_pdf)
        buttons_layout.addWidget(pdf_button)
        
        buttons_layout.addStretch()
        
        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.setFixedHeight(40)
        close_button.clicked.connect(self.close)
        buttons_layout.addWidget(close_button)
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 12px 24px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
    
    def create_preview_tab(self):
        """إنشاء تبويب المعاينة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # منطقة المعاينة
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setMinimumHeight(600)
        layout.addWidget(self.preview_text)
        
        return widget
    
    def create_quick_settings_tab(self):
        """إنشاء تبويب الإعدادات السريعة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # مجموعة الأحجام
        sizes_group = QGroupBox("📏 أحجام الخطوط")
        sizes_layout = QGridLayout(sizes_group)
        
        # حجم الخط الأساسي
        sizes_layout.addWidget(QLabel("حجم الخط الأساسي:"), 0, 0)
        self.base_size_spin = QSpinBox()
        self.base_size_spin.setRange(16, 60)
        self.base_size_spin.setValue(self.settings.get('base_font_size', 28))
        self.base_size_spin.valueChanged.connect(self.update_preview)
        sizes_layout.addWidget(self.base_size_spin, 0, 1)
        
        # حجم العناوين
        sizes_layout.addWidget(QLabel("حجم العناوين:"), 1, 0)
        self.title_size_spin = QSpinBox()
        self.title_size_spin.setRange(20, 80)
        self.title_size_spin.setValue(self.settings.get('clinic_name_size', 64))
        self.title_size_spin.valueChanged.connect(self.update_preview)
        sizes_layout.addWidget(self.title_size_spin, 1, 1)
        
        # حجم الأدوية
        sizes_layout.addWidget(QLabel("حجم خط الأدوية:"), 2, 0)
        self.medication_size_spin = QSpinBox()
        self.medication_size_spin.setRange(16, 50)
        self.medication_size_spin.setValue(self.settings.get('medication_size', 32))
        self.medication_size_spin.valueChanged.connect(self.update_preview)
        sizes_layout.addWidget(self.medication_size_spin, 2, 1)
        
        layout.addWidget(sizes_group)
        
        # مجموعة التخطيط
        layout_group = QGroupBox("📐 تخطيط الصفحة")
        layout_layout = QGridLayout(layout_group)
        
        # حجم الصفحة
        layout_layout.addWidget(QLabel("حجم الصفحة:"), 0, 0)
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["A4", "A5", "Letter"])
        self.page_size_combo.setCurrentText(self.settings.get('page_size', 'A4'))
        self.page_size_combo.currentTextChanged.connect(self.update_preview)
        layout_layout.addWidget(self.page_size_combo, 0, 1)
        
        # الهوامش
        layout_layout.addWidget(QLabel("هوامش الصفحة (mm):"), 1, 0)
        self.margins_spin = QSpinBox()
        self.margins_spin.setRange(5, 50)
        self.margins_spin.setValue(self.settings.get('page_margins', 10))
        self.margins_spin.valueChanged.connect(self.update_preview)
        layout_layout.addWidget(self.margins_spin, 1, 1)
        
        layout.addWidget(layout_group)
        
        # مجموعة العرض
        display_group = QGroupBox("👁️ عرض الأقسام")
        display_layout = QVBoxLayout(display_group)
        
        self.show_header_check = QCheckBox("عرض رأس العيادة")
        self.show_header_check.setChecked(self.settings.get('show_header', True))
        self.show_header_check.toggled.connect(self.update_preview)
        display_layout.addWidget(self.show_header_check)
        
        self.show_patient_check = QCheckBox("عرض معلومات المريض")
        self.show_patient_check.setChecked(self.settings.get('show_patient_info', True))
        self.show_patient_check.toggled.connect(self.update_preview)
        display_layout.addWidget(self.show_patient_check)
        
        self.show_diagnosis_check = QCheckBox("عرض التشخيص")
        self.show_diagnosis_check.setChecked(self.settings.get('show_diagnosis', True))
        self.show_diagnosis_check.toggled.connect(self.update_preview)
        display_layout.addWidget(self.show_diagnosis_check)
        
        self.show_prescription_check = QCheckBox("عرض الوصفة")
        self.show_prescription_check.setChecked(self.settings.get('show_prescription', True))
        self.show_prescription_check.toggled.connect(self.update_preview)
        display_layout.addWidget(self.show_prescription_check)
        
        self.show_signature_check = QCheckBox("عرض منطقة التوقيع")
        self.show_signature_check.setChecked(self.settings.get('show_signature', True))
        self.show_signature_check.toggled.connect(self.update_preview)
        display_layout.addWidget(self.show_signature_check)
        
        layout.addWidget(display_group)
        layout.addStretch()
        
        return widget
    
    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            settings_file = "prescription_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        
        # الإعدادات الافتراضية - محسنة للطباعة على A4/A5
        return {
            "base_font_size": 14,
            "clinic_name_size": 24,
            "doctor_name_size": 20,
            "prescription_size": 18,
            "medication_size": 16,
            "font_family": "Arial",
            "header_color": "#3498db",
            "patient_color": "#27ae60",
            "diagnosis_color": "#ff9800",
            "prescription_color": "#e74c3c",
            "page_margins": 15,
            "section_spacing": 20,
            "page_size": "A4",
            "show_header": True,
            "show_patient_info": True,
            "show_diagnosis": True,
            "show_prescription": True,
            "show_signature": True,
            "show_footer": True,
            "footer_text": "🏥 تم إنشاء هذه الوصفة بواسطة نظام إدارة العيادة الطبية",
            "signature_text": "توقيع الطبيب المعالج"
        }
    
    def get_current_settings(self):
        """الحصول على الإعدادات الحالية"""
        return {
            "base_font_size": self.base_size_spin.value(),
            "clinic_name_size": self.title_size_spin.value(),
            "medication_size": self.medication_size_spin.value(),
            "page_size": self.page_size_combo.currentText(),
            "page_margins": self.margins_spin.value(),
            "show_header": self.show_header_check.isChecked(),
            "show_patient_info": self.show_patient_check.isChecked(),
            "show_diagnosis": self.show_diagnosis_check.isChecked(),
            "show_prescription": self.show_prescription_check.isChecked(),
            "show_signature": self.show_signature_check.isChecked(),
            **self.settings  # دمج باقي الإعدادات
        }
    
    def update_preview(self):
        """تحديث المعاينة"""
        try:
            current_settings = self.get_current_settings()
            html = self.generate_report_html(current_settings)
            self.preview_text.setHtml(html)
        except Exception as e:
            print(f"خطأ في تحديث المعاينة: {e}")
    
    def generate_report_html(self, settings):
        """إنشاء HTML للتقرير"""
        current_date = datetime.now().strftime("%Y/%m/%d")
        current_time = datetime.now().strftime("%H:%M")
        
        # معلومات افتراضية للعيادة
        clinic_name = "عيادة طبية متخصصة"
        doctor_name = "د. طبيب متخصص"
        clinic_address = "العنوان الطبي"
        clinic_phone = "رقم الهاتف"
        
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="utf-8">
            <title>تقرير طبي - {self.patient_data['full_name']}</title>
            <style>
                @page {{
                    size: {settings['page_size']};
                    margin: {settings['page_margins']}mm;
                }}
                body {{
                    font-family: '{settings.get('font_family', 'Arial')}', sans-serif;
                    font-size: {settings['base_font_size']}px;
                    line-height: 1.6;
                    margin: 0;
                    padding: {settings['page_margins']}mm;
                    direction: rtl;
                    color: #2c3e50;
                    background: #ffffff;
                }}
                .header {{
                    background: linear-gradient(135deg, {settings.get('header_color', '#3498db')} 0%, #2980b9 100%);
                    color: white;
                    padding: 40px;
                    border-radius: 25px;
                    margin-bottom: 40px;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(52, 152, 219, 0.4);
                    display: {'block' if settings['show_header'] else 'none'};
                }}
                .clinic-name {{
                    font-size: {settings['clinic_name_size']}px;
                    font-weight: bold;
                    margin-bottom: 20px;
                    text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
                }}
                .doctor-name {{
                    font-size: {int(settings['clinic_name_size'] * 0.7)}px;
                    margin-bottom: 15px;
                    opacity: 0.95;
                }}
                .clinic-info {{
                    font-size: {int(settings['clinic_name_size'] * 0.5)}px;
                    opacity: 0.9;
                }}
                .patient-info {{
                    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
                    border: 4px solid {settings.get('patient_color', '#27ae60')};
                    border-radius: 20px;
                    padding: 30px;
                    margin: 30px 0;
                    display: {'block' if settings['show_patient_info'] else 'none'};
                }}
                .patient-title {{
                    font-size: {int(settings['clinic_name_size'] * 0.6)}px;
                    font-weight: bold;
                    color: {settings.get('patient_color', '#27ae60')};
                    text-align: center;
                    margin-bottom: 20px;
                }}
                .patient-details {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    font-size: {int(settings['base_font_size'] * 1.1)}px;
                }}
                .patient-detail {{
                    background: white;
                    padding: 20px;
                    border-radius: 15px;
                    border-right: 6px solid {settings.get('patient_color', '#27ae60')};
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }}
                .diagnosis-section {{
                    margin: 40px 0;
                    display: {'block' if settings['show_diagnosis'] else 'none'};
                }}
                .diagnosis-title {{
                    font-size: {int(settings['clinic_name_size'] * 0.5)}px;
                    font-weight: bold;
                    color: {settings.get('diagnosis_color', '#ff9800')};
                    text-align: center;
                    background: white;
                    padding: 20px;
                    border-radius: 15px;
                    margin-bottom: 20px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }}
                .diagnosis-content {{
                    background: white;
                    padding: 25px;
                    border-radius: 15px;
                    border-right: 6px solid {settings.get('diagnosis_color', '#ff9800')};
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    font-size: {int(settings['base_font_size'] * 1.2)}px;
                    line-height: 1.8;
                }}
                .treatment {{
                    margin: 40px 0;
                    display: {'block' if settings['show_prescription'] else 'none'};
                }}
                .treatment-title {{
                    font-size: {int(settings['clinic_name_size'] * 0.8)}px;
                    font-weight: bold;
                    color: white;
                    background: linear-gradient(135deg, {settings.get('prescription_color', '#e74c3c')} 0%, #c0392b 100%);
                    padding: 30px;
                    border-radius: 20px;
                    margin-bottom: 30px;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(231, 76, 60, 0.3);
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                }}
                .treatment-content {{
                    background: linear-gradient(135deg, #fdf2f2 0%, #fef9e7 100%);
                    border: 4px solid {settings.get('prescription_color', '#e74c3c')};
                    border-radius: 20px;
                    padding: 30px;
                    min-height: 200px;
                    box-shadow: 0 10px 30px rgba(231, 76, 60, 0.2);
                }}
                .medication-item {{
                    background: white;
                    margin: 20px 0;
                    padding: 25px;
                    border-radius: 15px;
                    border-right: 6px solid {settings.get('prescription_color', '#e74c3c')};
                    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
                    font-size: {settings['medication_size']}px;
                    line-height: 1.8;
                    font-weight: 500;
                }}
                .signature {{
                    margin-top: 60px;
                    text-align: center;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    padding: 30px;
                    border-radius: 20px;
                    border: 2px solid #dee2e6;
                    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
                    display: {'block' if settings['show_signature'] else 'none'};
                }}
                .signature-line {{
                    border-top: 3px solid #2c3e50;
                    width: 250px;
                    margin: 30px auto 15px auto;
                }}
                .signature-text {{
                    font-weight: bold;
                    color: #2c3e50;
                    margin-top: 15px;
                    font-size: {int(settings['base_font_size'] * 1.2)}px;
                }}
                @media print {{
                    @page {{
                        size: {settings['page_size']};
                        margin: {settings['page_margins']}mm;
                    }}
                    body {{
                        font-size: {int(settings['base_font_size'] * 1.1)}px !important;
                        line-height: 1.4 !important;
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }}
                    * {{
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }}
                }}
            </style>
        </head>
        <body>
        """
        
        # رأس العيادة
        if settings['show_header']:
            html += f"""
            <div class="header">
                <div class="clinic-name">{clinic_name}</div>
                <div class="doctor-name">{doctor_name}</div>
                <div class="clinic-info">{clinic_address} | {clinic_phone}</div>
            </div>
            """
        
        # معلومات المريض
        if settings['show_patient_info']:
            html += f"""
            <div class="patient-info">
                <div class="patient-title">👤 معلومات المريض</div>
                <div class="patient-details">
                    <div class="patient-detail">
                        <strong>الاسم:</strong> {self.patient_data['full_name']}
                    </div>
                    <div class="patient-detail">
                        <strong>العمر:</strong> {self.patient_data.get('age', 'غير محدد')}
                    </div>
                    <div class="patient-detail">
                        <strong>الهاتف:</strong> {self.patient_data.get('phone', 'غير محدد')}
                    </div>
                    <div class="patient-detail">
                        <strong>تاريخ الزيارة:</strong> {current_date}
                    </div>
                </div>
            </div>
            """
        
        # التشخيص
        if settings['show_diagnosis'] and self.visit_data.get('diagnosis'):
            html += f"""
            <div class="diagnosis-section">
                <div class="diagnosis-title">🔍 التشخيص</div>
                <div class="diagnosis-content">
                    {self.visit_data['diagnosis']}
                </div>
            </div>
            """
        
        # الوصفة الطبية
        if settings['show_prescription'] and self.treatment_text:
            formatted_treatment = self.format_treatment_for_print(self.treatment_text, settings)
            html += f"""
            <div class="treatment">
                <div class="treatment-title">💊 الوصفة الطبية</div>
                <div class="treatment-content">
                    {formatted_treatment}
                </div>
            </div>
            """
        
        # التوقيع
        if settings['show_signature']:
            html += f"""
            <div class="signature">
                <div class="signature-line"></div>
                <div class="signature-text">{settings.get('signature_text', 'توقيع الطبيب المعالج')}</div>
            </div>
            """
        
        html += """
        </body>
        </html>
        """
        
        return html
    
    def format_treatment_for_print(self, treatment, settings):
        """تنسيق الوصفة للطباعة"""
        if not treatment:
            return '<div class="medication-item">لا توجد وصفة طبية</div>'
        
        lines = treatment.split('\n')
        formatted_lines = []
        counter = 1
        
        for line in lines:
            line = line.strip()
            if line:
                if not line[0].isdigit():
                    line = f"{counter}. {line}"
                    counter += 1
                formatted_lines.append(f'<div class="medication-item">{line}</div>')
        
        return '\n'.join(formatted_lines)
    
    def open_advanced_settings(self):
        """فتح الإعدادات المتقدمة"""
        try:
            from ui.prescription_settings_widget import PrescriptionSettingsDialog
            
            settings_dialog = PrescriptionSettingsDialog(self)
            if settings_dialog.exec() == QDialog.DialogCode.Accepted:
                self.settings = self.load_settings()
                self.update_preview()
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الإعدادات المتقدمة: {str(e)}")
    
    def print_report(self):
        """طباعة التقرير"""
        if not PRINT_SUPPORT_AVAILABLE:
            QMessageBox.warning(self, "خطأ", "مكتبة الطباعة غير متوفرة")
            return
        
        try:
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            
            # إعداد حجم الصفحة
            current_settings = self.get_current_settings()
            if current_settings['page_size'] == 'A5':
                from PyQt6.QtGui import QPageSize
                printer.setPageSize(QPageSize(QPageSize.PageSizeId.A5))
            
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec() == QPrintDialog.DialogCode.Accepted:
                document = QTextDocument()
                document.setHtml(self.generate_report_html(current_settings))
                document.print(printer)
                QMessageBox.information(self, "نجح", "تم طباعة التقرير بنجاح")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير: {str(e)}")
    
    def save_pdf(self):
        """حفظ التقرير كـ PDF"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            
            filename, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير", 
                f"تقرير_{self.patient_data['full_name']}_{datetime.now().strftime('%Y%m%d')}.pdf",
                "PDF Files (*.pdf)"
            )
            
            if filename:
                printer = QPrinter(QPrinter.PrinterMode.HighResolution)
                printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)
                printer.setOutputFileName(filename)
                
                current_settings = self.get_current_settings()
                document = QTextDocument()
                document.setHtml(self.generate_report_html(current_settings))
                document.print(printer)
                
                QMessageBox.information(self, "نجح", f"تم حفظ التقرير في:\n{filename}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ PDF: {str(e)}")
