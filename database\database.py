import sqlite3
import os
from datetime import datetime
import shutil

class DatabaseManager:
    def __init__(self, db_path="clinic_database.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """إنشاء اتصال جديد بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('doctor', 'secretary', 'admin')),
                full_name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المرضى
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_number TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                phone TEXT,
                gender TEXT CHECK (gender IN ('male', 'female')),
                age INTEGER,
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الزيارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS visits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER NOT NULL,
                visit_date DATE NOT NULL,
                weight REAL,
                blood_sugar REAL,
                blood_pressure TEXT,
                diagnosis TEXT,
                notes TEXT,
                treatment_description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id) ON DELETE CASCADE
            )
        ''')
        
        # جدول الصور المرفقة بالزيارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS visit_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                visit_id INTEGER NOT NULL,
                image_path TEXT NOT NULL,
                image_type TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (visit_id) REFERENCES visits (id) ON DELETE CASCADE
            )
        ''')
        
        # جدول الأدوية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS medications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                usage_count INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الإعدادات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول التشخيصات الشائعة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS diagnoses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                usage_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج البيانات الافتراضية
        self._insert_default_data(cursor)

        # إضافة التشخيصات الافتراضية
        self._insert_default_diagnoses(cursor)
        
        conn.commit()
        conn.close()

        # تحديث قاعدة البيانات (إضافة أعمدة جديدة)
        self.update_database_schema()

    def update_database_schema(self):
        """تحديث هيكل قاعدة البيانات لإضافة أعمدة جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود عمود التشخيص في جدول الزيارات
            cursor.execute("PRAGMA table_info(visits)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'diagnosis' not in columns:
                cursor.execute("ALTER TABLE visits ADD COLUMN diagnosis TEXT")
                print("تم إضافة عمود التشخيص إلى جدول الزيارات")

            conn.commit()
        except Exception as e:
            print(f"خطأ في تحديث قاعدة البيانات: {e}")
        finally:
            conn.close()

    def _insert_default_data(self, cursor):
        """إدراج البيانات الافتراضية"""
        # إنشاء مستخدم افتراضي للطبيب
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password, role, full_name)
            VALUES ('doctor', 'doctor123', 'doctor', 'د. أحمد محمد')
        ''')
        
        # إنشاء مستخدم افتراضي للسكرتير
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password, role, full_name)
            VALUES ('secretary', 'secretary123', 'secretary', 'فاطمة أحمد')
        ''')

        # إنشاء مستخدم افتراضي للمدير
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password, role, full_name)
            VALUES ('admin', 'admin123', 'admin', 'محمد الإدارة')
        ''')
        
        # إعدادات افتراضية
        default_settings = [
            ('clinic_name', 'عيادة الدكتور أحمد محمد', 'اسم العيادة'),
            ('doctor_name', 'د. أحمد محمد', 'اسم الطبيب'),
            ('clinic_address', 'شارع الملك فهد، الرياض', 'عنوان العيادة'),
            ('clinic_phone', '011-1234567', 'هاتف العيادة'),
            ('logo_path', '', 'مسار شعار العيادة'),
            ('backup_folder', '', 'مجلد النسخ الاحتياطي'),
            ('network_mode', 'standalone', 'وضع الشبكة (standalone/master/slave)'),
            ('master_ip', '', 'IP الجهاز الرئيسي'),
            ('auto_backup', 'true', 'النسخ الاحتياطي التلقائي')
        ]
        
        for key, value, description in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO settings (key, value, description)
                VALUES (?, ?, ?)
            ''', (key, value, description))

    def _insert_default_diagnoses(self, cursor):
        """إدراج التشخيصات الافتراضية"""
        default_diagnoses = [
            ("نزلة برد", "التهاب في الجهاز التنفسي العلوي"),
            ("صداع", "ألم في الرأس"),
            ("التهاب الحلق", "التهاب في منطقة الحلق"),
            ("حمى", "ارتفاع في درجة حرارة الجسم"),
            ("آلام المعدة", "ألم في منطقة البطن"),
            ("التهاب المفاصل", "التهاب في المفاصل"),
            ("ضغط الدم المرتفع", "ارتفاع في ضغط الدم"),
            ("السكري", "مرض السكري"),
            ("الربو", "مرض في الجهاز التنفسي"),
            ("الأنيميا", "فقر الدم")
        ]

        for name, description in default_diagnoses:
            cursor.execute('''
                INSERT OR IGNORE INTO diagnoses (name, description, usage_count)
                VALUES (?, ?, 0)
            ''', (name, description))

    def backup_database(self, backup_folder=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        if not backup_folder:
            backup_folder = self.get_setting('backup_folder')
            if not backup_folder:
                backup_folder = 'backups'
        
        if not os.path.exists(backup_folder):
            os.makedirs(backup_folder)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"clinic_backup_{timestamp}.db"
        backup_path = os.path.join(backup_folder, backup_filename)
        
        try:
            shutil.copy2(self.db_path, backup_path)
            return backup_path
        except Exception as e:
            raise Exception(f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
    
    def get_setting(self, key):
        """الحصول على قيمة إعداد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT value FROM settings WHERE key = ?', (key,))
        result = cursor.fetchone()
        conn.close()
        return result['value'] if result else None
    
    def set_setting(self, key, value):
        """تحديث قيمة إعداد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO settings (key, value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        ''', (key, value))
        conn.commit()
        conn.close()
