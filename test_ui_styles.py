#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للواجهة والأنماط الجديدة
Quick UI and Styles Test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTableWidget, QTableWidgetItem
from PyQt6.QtCore import Qt
from styles.style_manager import StyleManager

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.style_manager = StyleManager()
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار الأنماط والخطوط الجديدة")
        self.setGeometry(100, 100, 800, 600)
        
        # تطبيق الأنماط
        combined_style = (
            self.style_manager.get_main_style() +
            self.style_manager.get_button_style() +
            self.style_manager.get_table_style() +
            self.style_manager.get_input_style() +
            self.style_manager.get_frame_style()
        )
        self.setStyleSheet(combined_style)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # العنوان الرئيسي
        main_title = QLabel("🏥 اختبار النظام الطبي")
        main_title.setObjectName("mainTitle")
        main_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(main_title)
        
        # عنوان فرعي
        sub_title = QLabel("اختبار الخطوط والأنماط الجديدة")
        sub_title.setObjectName("sectionTitle")
        sub_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(sub_title)
        
        # نص عادي
        content_text = QLabel("هذا النص يظهر بحجم خط مناسب للقراءة والاستخدام اليومي في النظام الطبي")
        content_text.setObjectName("contentText")
        content_text.setWordWrap(True)
        layout.addWidget(content_text)
        
        # أزرار للاختبار
        button1 = QPushButton("إضافة مريض جديد")
        button2 = QPushButton("عرض الزيارات")
        button3 = QPushButton("طباعة وصفة")
        
        layout.addWidget(button1)
        layout.addWidget(button2)
        layout.addWidget(button3)
        
        # جدول للاختبار
        table = QTableWidget(3, 4)
        table.setObjectName("dataTable")
        table.setHorizontalHeaderLabels(["اسم المريض", "رقم الهاتف", "العمر", "التاريخ"])
        
        # إضافة بيانات تجريبية
        test_data = [
            ["محمد أحمد", "0123456789", "35", "2025/06/26"],
            ["فاطمة محمود", "0198765432", "28", "2025/06/26"],
            ["علي حسن", "0111222333", "42", "2025/06/26"]
        ]
        
        for row, data in enumerate(test_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                table.setItem(row, col, item)
        
        table.resizeColumnsToContents()
        layout.addWidget(table)

def main():
    app = QApplication(sys.argv)
    
    # ضبط اتجاه القراءة من اليمين إلى اليسار
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    window = TestWindow()
    window.show()
    
    return app.exec()

if __name__ == '__main__':
    sys.exit(main())
