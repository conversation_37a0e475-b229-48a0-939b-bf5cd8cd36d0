from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QGroupBox, QSpinBox, QCheckBox,
                            QComboBox, QLineEdit, QTextEdit, QTabWidget,
                            QWidget, QGridLayout, QColorDialog, QMessageBox,
                            QSlider, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor
import json
import os

class PrescriptionSettingsDialog(QDialog):
    """نافذة إعدادات تخصيص الوصفة"""
    
    settings_changed = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("⚙️ إعدادات تخصيص الوصفة")
        self.setFixedSize(800, 700)
        self.setModal(True)
        
        # تحميل الإعدادات الحالية
        self.settings = self.load_settings()
        
        self.init_ui()
        self.load_current_settings()
        
    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # إنشاء التبويبات
        tab_widget = QTabWidget()
        
        # تبويب الخطوط والأحجام
        fonts_tab = self.create_fonts_tab()
        tab_widget.addTab(fonts_tab, "🔤 الخطوط والأحجام")
        
        # تبويب الألوان
        colors_tab = self.create_colors_tab()
        tab_widget.addTab(colors_tab, "🎨 الألوان")
        
        # تبويب التخطيط
        layout_tab = self.create_layout_tab()
        tab_widget.addTab(layout_tab, "📐 التخطيط")
        
        # تبويب المحتوى
        content_tab = self.create_content_tab()
        tab_widget.addTab(content_tab, "📝 المحتوى")
        
        layout.addWidget(tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        # زر المعاينة
        preview_button = QPushButton("👁️ معاينة")
        preview_button.setFixedHeight(40)
        preview_button.clicked.connect(self.preview_settings)
        buttons_layout.addWidget(preview_button)
        
        # زر الإعدادات الافتراضية
        default_button = QPushButton("🔄 افتراضي")
        default_button.setFixedHeight(40)
        default_button.clicked.connect(self.reset_to_default)
        buttons_layout.addWidget(default_button)
        
        buttons_layout.addStretch()
        
        # زر الحفظ
        save_button = QPushButton("💾 حفظ")
        save_button.setFixedHeight(40)
        save_button.clicked.connect(self.save_settings)
        buttons_layout.addWidget(save_button)
        
        # زر الإلغاء
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(40)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QSpinBox, QComboBox, QLineEdit {
                padding: 8px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
            }
            QSpinBox:focus, QComboBox:focus, QLineEdit:focus {
                border-color: #3498db;
            }
        """)
    
    def create_fonts_tab(self):
        """إنشاء تبويب الخطوط والأحجام"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # مجموعة أحجام الخطوط
        fonts_group = QGroupBox("📏 أحجام الخطوط")
        fonts_layout = QGridLayout(fonts_group)
        
        # حجم الخط الأساسي
        fonts_layout.addWidget(QLabel("حجم الخط الأساسي:"), 0, 0)
        self.base_font_size = QSpinBox()
        self.base_font_size.setRange(12, 50)
        self.base_font_size.setSuffix(" px")
        fonts_layout.addWidget(self.base_font_size, 0, 1)
        
        # حجم خط اسم العيادة
        fonts_layout.addWidget(QLabel("حجم خط اسم العيادة:"), 1, 0)
        self.clinic_name_size = QSpinBox()
        self.clinic_name_size.setRange(20, 80)
        self.clinic_name_size.setSuffix(" px")
        fonts_layout.addWidget(self.clinic_name_size, 1, 1)
        
        # حجم خط اسم الطبيب
        fonts_layout.addWidget(QLabel("حجم خط اسم الطبيب:"), 2, 0)
        self.doctor_name_size = QSpinBox()
        self.doctor_name_size.setRange(16, 60)
        self.doctor_name_size.setSuffix(" px")
        fonts_layout.addWidget(self.doctor_name_size, 2, 1)
        
        # حجم خط الوصفة
        fonts_layout.addWidget(QLabel("حجم خط الوصفة:"), 3, 0)
        self.prescription_size = QSpinBox()
        self.prescription_size.setRange(16, 60)
        self.prescription_size.setSuffix(" px")
        fonts_layout.addWidget(self.prescription_size, 3, 1)
        
        # حجم خط الأدوية
        fonts_layout.addWidget(QLabel("حجم خط الأدوية:"), 4, 0)
        self.medication_size = QSpinBox()
        self.medication_size.setRange(14, 50)
        self.medication_size.setSuffix(" px")
        fonts_layout.addWidget(self.medication_size, 4, 1)
        
        layout.addWidget(fonts_group)
        
        # مجموعة نوع الخط
        font_family_group = QGroupBox("🔤 نوع الخط")
        font_family_layout = QGridLayout(font_family_group)
        
        font_family_layout.addWidget(QLabel("نوع الخط:"), 0, 0)
        self.font_family = QComboBox()
        self.font_family.addItems([
            "Arial", "Tahoma", "Segoe UI", "Times New Roman", 
            "Calibri", "Verdana", "Georgia", "Comic Sans MS"
        ])
        font_family_layout.addWidget(self.font_family, 0, 1)
        
        layout.addWidget(font_family_group)
        layout.addStretch()
        
        return widget
    
    def create_colors_tab(self):
        """إنشاء تبويب الألوان"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # مجموعة ألوان الأقسام
        colors_group = QGroupBox("🎨 ألوان الأقسام")
        colors_layout = QGridLayout(colors_group)
        
        # لون رأس العيادة
        colors_layout.addWidget(QLabel("لون رأس العيادة:"), 0, 0)
        self.header_color_button = QPushButton("اختيار اللون")
        self.header_color_button.clicked.connect(lambda: self.choose_color('header'))
        colors_layout.addWidget(self.header_color_button, 0, 1)
        
        # لون معلومات المريض
        colors_layout.addWidget(QLabel("لون معلومات المريض:"), 1, 0)
        self.patient_color_button = QPushButton("اختيار اللون")
        self.patient_color_button.clicked.connect(lambda: self.choose_color('patient'))
        colors_layout.addWidget(self.patient_color_button, 1, 1)
        
        # لون التشخيص
        colors_layout.addWidget(QLabel("لون التشخيص:"), 2, 0)
        self.diagnosis_color_button = QPushButton("اختيار اللون")
        self.diagnosis_color_button.clicked.connect(lambda: self.choose_color('diagnosis'))
        colors_layout.addWidget(self.diagnosis_color_button, 2, 1)
        
        # لون الوصفة
        colors_layout.addWidget(QLabel("لون الوصفة:"), 3, 0)
        self.prescription_color_button = QPushButton("اختيار اللون")
        self.prescription_color_button.clicked.connect(lambda: self.choose_color('prescription'))
        colors_layout.addWidget(self.prescription_color_button, 3, 1)
        
        layout.addWidget(colors_group)
        layout.addStretch()
        
        return widget
    
    def create_layout_tab(self):
        """إنشاء تبويب التخطيط"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # مجموعة الهوامش والمسافات
        margins_group = QGroupBox("📐 الهوامش والمسافات")
        margins_layout = QGridLayout(margins_group)
        
        # هوامش الصفحة
        margins_layout.addWidget(QLabel("هوامش الصفحة:"), 0, 0)
        self.page_margins = QSpinBox()
        self.page_margins.setRange(5, 50)
        self.page_margins.setSuffix(" mm")
        margins_layout.addWidget(self.page_margins, 0, 1)
        
        # المسافة بين الأقسام
        margins_layout.addWidget(QLabel("المسافة بين الأقسام:"), 1, 0)
        self.section_spacing = QSpinBox()
        self.section_spacing.setRange(10, 100)
        self.section_spacing.setSuffix(" px")
        margins_layout.addWidget(self.section_spacing, 1, 1)
        
        # حجم الصفحة
        margins_layout.addWidget(QLabel("حجم الصفحة:"), 2, 0)
        self.page_size = QComboBox()
        self.page_size.addItems(["A4", "A5", "Letter"])
        margins_layout.addWidget(self.page_size, 2, 1)
        
        layout.addWidget(margins_group)
        
        # مجموعة ترتيب الأقسام
        order_group = QGroupBox("📋 ترتيب الأقسام")
        order_layout = QVBoxLayout(order_group)
        
        # خانات اختيار الأقسام
        self.show_header = QCheckBox("عرض رأس العيادة")
        self.show_patient_info = QCheckBox("عرض معلومات المريض")
        self.show_diagnosis = QCheckBox("عرض التشخيص")
        self.show_prescription = QCheckBox("عرض الوصفة")
        self.show_signature = QCheckBox("عرض منطقة التوقيع")
        self.show_footer = QCheckBox("عرض التذييل")
        
        order_layout.addWidget(self.show_header)
        order_layout.addWidget(self.show_patient_info)
        order_layout.addWidget(self.show_diagnosis)
        order_layout.addWidget(self.show_prescription)
        order_layout.addWidget(self.show_signature)
        order_layout.addWidget(self.show_footer)
        
        layout.addWidget(order_group)
        layout.addStretch()
        
        return widget
    
    def create_content_tab(self):
        """إنشاء تبويب المحتوى"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # مجموعة النصوص المخصصة
        text_group = QGroupBox("📝 النصوص المخصصة")
        text_layout = QGridLayout(text_group)
        
        # نص التذييل
        text_layout.addWidget(QLabel("نص التذييل:"), 0, 0)
        self.footer_text = QTextEdit()
        self.footer_text.setMaximumHeight(80)
        text_layout.addWidget(self.footer_text, 0, 1)
        
        # نص التوقيع
        text_layout.addWidget(QLabel("نص التوقيع:"), 1, 0)
        self.signature_text = QLineEdit()
        text_layout.addWidget(self.signature_text, 1, 1)
        
        layout.addWidget(text_group)
        layout.addStretch()
        
        return widget
    
    def choose_color(self, section):
        """اختيار لون لقسم معين"""
        color = QColorDialog.getColor(QColor("#3498db"), self)
        if color.isValid():
            button = getattr(self, f"{section}_color_button")
            button.setStyleSheet(f"background-color: {color.name()}; color: white;")
            button.setText(color.name())
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        settings_file = "prescription_settings.json"
        default_settings = {
            "base_font_size": 28,
            "clinic_name_size": 64,
            "doctor_name_size": 48,
            "prescription_size": 56,
            "medication_size": 32,
            "font_family": "Arial",
            "header_color": "#3498db",
            "patient_color": "#27ae60",
            "diagnosis_color": "#ff9800",
            "prescription_color": "#e74c3c",
            "page_margins": 10,
            "section_spacing": 40,
            "page_size": "A4",
            "show_header": True,
            "show_patient_info": True,
            "show_diagnosis": True,
            "show_prescription": True,
            "show_signature": True,
            "show_footer": True,
            "footer_text": "🏥 تم إنشاء هذه الوصفة بواسطة نظام إدارة العيادة الطبية",
            "signature_text": "توقيع الطبيب المعالج"
        }
        
        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    default_settings.update(saved_settings)
            except:
                pass
        
        return default_settings
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية في الواجهة"""
        # أحجام الخطوط
        self.base_font_size.setValue(self.settings["base_font_size"])
        self.clinic_name_size.setValue(self.settings["clinic_name_size"])
        self.doctor_name_size.setValue(self.settings["doctor_name_size"])
        self.prescription_size.setValue(self.settings["prescription_size"])
        self.medication_size.setValue(self.settings["medication_size"])
        
        # نوع الخط
        self.font_family.setCurrentText(self.settings["font_family"])
        
        # الألوان
        self.update_color_button("header", self.settings["header_color"])
        self.update_color_button("patient", self.settings["patient_color"])
        self.update_color_button("diagnosis", self.settings["diagnosis_color"])
        self.update_color_button("prescription", self.settings["prescription_color"])
        
        # التخطيط
        self.page_margins.setValue(self.settings["page_margins"])
        self.section_spacing.setValue(self.settings["section_spacing"])
        self.page_size.setCurrentText(self.settings["page_size"])
        
        # الأقسام
        self.show_header.setChecked(self.settings["show_header"])
        self.show_patient_info.setChecked(self.settings["show_patient_info"])
        self.show_diagnosis.setChecked(self.settings["show_diagnosis"])
        self.show_prescription.setChecked(self.settings["show_prescription"])
        self.show_signature.setChecked(self.settings["show_signature"])
        self.show_footer.setChecked(self.settings["show_footer"])
        
        # النصوص
        self.footer_text.setPlainText(self.settings["footer_text"])
        self.signature_text.setText(self.settings["signature_text"])
    
    def update_color_button(self, section, color):
        """تحديث لون الزر"""
        button = getattr(self, f"{section}_color_button")
        button.setStyleSheet(f"background-color: {color}; color: white;")
        button.setText(color)
    
    def get_current_settings(self):
        """الحصول على الإعدادات الحالية"""
        return {
            "base_font_size": self.base_font_size.value(),
            "clinic_name_size": self.clinic_name_size.value(),
            "doctor_name_size": self.doctor_name_size.value(),
            "prescription_size": self.prescription_size.value(),
            "medication_size": self.medication_size.value(),
            "font_family": self.font_family.currentText(),
            "header_color": self.header_color_button.text(),
            "patient_color": self.patient_color_button.text(),
            "diagnosis_color": self.diagnosis_color_button.text(),
            "prescription_color": self.prescription_color_button.text(),
            "page_margins": self.page_margins.value(),
            "section_spacing": self.section_spacing.value(),
            "page_size": self.page_size.currentText(),
            "show_header": self.show_header.isChecked(),
            "show_patient_info": self.show_patient_info.isChecked(),
            "show_diagnosis": self.show_diagnosis.isChecked(),
            "show_prescription": self.show_prescription.isChecked(),
            "show_signature": self.show_signature.isChecked(),
            "show_footer": self.show_footer.isChecked(),
            "footer_text": self.footer_text.toPlainText(),
            "signature_text": self.signature_text.text()
        }
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings = self.get_current_settings()
            with open("prescription_settings.json", 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            self.settings_changed.emit(settings)
            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")
    
    def reset_to_default(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        self.settings = self.load_settings()
        self.load_current_settings()
        QMessageBox.information(self, "تم", "تم إعادة تعيين الإعدادات الافتراضية")
    
    def preview_settings(self):
        """معاينة الإعدادات"""
        QMessageBox.information(self, "معاينة", "سيتم تطوير ميزة المعاينة قريباً")
