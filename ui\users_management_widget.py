from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QMessageBox, QDialog, QComboBox, QHeaderView, QFrame,
                            QFormLayout, QGroupBox, QTabWidget)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from models.user import User

class ChangePasswordDialog(QDialog):
    """نافذة تغيير كلمة المرور"""
    
    def __init__(self, user_model, user_data, parent=None):
        super().__init__(parent)
        self.user_model = user_model
        self.user_data = user_data
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"تغيير كلمة المرور - {self.user_data['full_name']}")
        self.setFixedSize(400, 350)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان النافذة
        title_label = QLabel("🔒 تغيير كلمة المرور")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # معلومات المستخدم
        user_info = QLabel(f"المستخدم: {self.user_data['full_name']}\nاسم المستخدم: {self.user_data['username']}")
        user_info.setStyleSheet("""
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            color: #495057;
        """)
        layout.addWidget(user_info)
        
        # نموذج تغيير كلمة المرور
        form_layout = QFormLayout()
        
        # كلمة المرور القديمة
        old_password_label = QLabel("كلمة المرور الحالية:")
        old_password_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        form_layout.addRow(old_password_label)
        
        self.old_password_input = QLineEdit()
        self.old_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.old_password_input.setFixedHeight(35)
        form_layout.addRow(self.old_password_input)
        
        # كلمة المرور الجديدة
        new_password_label = QLabel("كلمة المرور الجديدة:")
        new_password_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        form_layout.addRow(new_password_label)
        
        self.new_password_input = QLineEdit()
        self.new_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.new_password_input.setFixedHeight(35)
        form_layout.addRow(self.new_password_input)
        
        # تأكيد كلمة المرور
        confirm_password_label = QLabel("تأكيد كلمة المرور:")
        confirm_password_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        form_layout.addRow(confirm_password_label)
        
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.confirm_password_input.setFixedHeight(35)
        form_layout.addRow(self.confirm_password_input)
        
        layout.addLayout(form_layout)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        save_button = QPushButton("💾 حفظ")
        save_button.setFixedHeight(40)
        save_button.setFixedWidth(120)
        save_button.clicked.connect(self.change_password)
        buttons_layout.addWidget(save_button)
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(40)
        cancel_button.setFixedWidth(120)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                margin-bottom: 5px;
            }
            QLineEdit {
                padding: 10px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
    
    def change_password(self):
        """تغيير كلمة المرور"""
        old_password = self.old_password_input.text()
        new_password = self.new_password_input.text()
        confirm_password = self.confirm_password_input.text()
        
        if not old_password or not new_password or not confirm_password:
            QMessageBox.warning(self, "خطأ", "يرجى ملء جميع الحقول")
            return
        
        if new_password != confirm_password:
            QMessageBox.warning(self, "خطأ", "كلمة المرور الجديدة وتأكيدها غير متطابقين")
            return
        
        if len(new_password) < 6:
            QMessageBox.warning(self, "خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return
        
        try:
            success = self.user_model.change_password(
                self.user_data['id'], old_password, new_password
            )
            if success:
                QMessageBox.information(self, "نجح", "تم تغيير كلمة المرور بنجاح")
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في تغيير كلمة المرور")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

class UserDialog(QDialog):
    """نافذة إضافة/تعديل مستخدم"""
    
    def __init__(self, user_model, user_data=None, parent=None):
        super().__init__(parent)
        self.user_model = user_model
        self.user_data = user_data
        self.is_edit_mode = user_data is not None
        self.init_ui()
        
        if self.is_edit_mode:
            self.load_user_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل مستخدم" if self.is_edit_mode else "إضافة مستخدم جديد"
        self.setWindowTitle(title)
        self.setFixedSize(450, 400)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        form_layout.addRow(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setFixedHeight(35)
        if self.is_edit_mode:
            self.username_input.setReadOnly(True)
        form_layout.addRow(self.username_input)
        
        # الاسم الكامل
        full_name_label = QLabel("الاسم الكامل:")
        full_name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        form_layout.addRow(full_name_label)
        
        self.full_name_input = QLineEdit()
        self.full_name_input.setFixedHeight(35)
        form_layout.addRow(self.full_name_input)
        
        # الدور
        role_label = QLabel("الدور:")
        role_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        form_layout.addRow(role_label)
        
        self.role_combo = QComboBox()
        self.role_combo.addItems(["طبيب", "سكرتير", "مدير"])
        self.role_combo.setFixedHeight(35)
        form_layout.addRow(self.role_combo)
        
        # كلمة المرور (للمستخدمين الجدد فقط)
        if not self.is_edit_mode:
            password_label = QLabel("كلمة المرور:")
            password_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            form_layout.addRow(password_label)
            
            self.password_input = QLineEdit()
            self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
            self.password_input.setFixedHeight(35)
            form_layout.addRow(self.password_input)
        
        layout.addLayout(form_layout)
        
        # مساحة فارغة
        layout.addStretch()
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        save_button = QPushButton("💾 حفظ")
        save_button.setFixedHeight(40)
        save_button.setFixedWidth(120)
        save_button.clicked.connect(self.save_user)
        buttons_layout.addWidget(save_button)
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(40)
        cancel_button.setFixedWidth(120)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                margin-bottom: 5px;
            }
            QLineEdit, QComboBox {
                padding: 10px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
    
    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        if self.user_data:
            self.username_input.setText(self.user_data['username'])
            self.full_name_input.setText(self.user_data['full_name'])
            
            role_mapping = {"doctor": "طبيب", "secretary": "سكرتير", "admin": "مدير"}
            role_text = role_mapping.get(self.user_data['role'], "طبيب")
            self.role_combo.setCurrentText(role_text)
    
    def save_user(self):
        """حفظ المستخدم"""
        username = self.username_input.text().strip()
        full_name = self.full_name_input.text().strip()
        role_text = self.role_combo.currentText()
        
        if not username or not full_name:
            QMessageBox.warning(self, "خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return
        
        # تحويل النص العربي إلى الدور الإنجليزي
        role_mapping = {"طبيب": "doctor", "سكرتير": "secretary", "مدير": "admin"}
        role = role_mapping.get(role_text, "doctor")
        
        try:
            if self.is_edit_mode:
                # تحديث المستخدم
                success = self.user_model.update_user(
                    self.user_data['id'],
                    full_name=full_name,
                    role=role
                )
                if success:
                    QMessageBox.information(self, "نجح", "تم تحديث المستخدم بنجاح")
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تحديث المستخدم")
            else:
                # إضافة مستخدم جديد
                password = self.password_input.text()
                if not password:
                    QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور")
                    return
                
                if len(password) < 6:
                    QMessageBox.warning(self, "خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                    return
                
                user_id = self.user_model.add_user(username, password, role, full_name)
                if user_id:
                    QMessageBox.information(self, "نجح", "تم إضافة المستخدم بنجاح")
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إضافة المستخدم")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

class UsersManagementWidget(QWidget):
    """ويدجت إدارة المستخدمين"""
    
    def __init__(self, user_model, current_user, parent=None):
        super().__init__(parent)
        self.user_model = user_model
        self.current_user = current_user
        self.current_users = []
        self.init_ui()
        self.load_users()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        header_layout = QHBoxLayout(header_frame)
        
        title_label = QLabel("👥 إدارة المستخدمين")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; border: none; padding: 0;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # زر إضافة مستخدم (للمدير فقط)
        if self.current_user['role'] == 'admin':
            add_user_button = QPushButton("➕ إضافة مستخدم")
            add_user_button.setFixedHeight(35)
            add_user_button.clicked.connect(self.add_user)
            header_layout.addWidget(add_user_button)
        
        layout.addWidget(header_frame)
        
        # جدول المستخدمين
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(15, 15, 15, 15)
        
        table_title = QLabel("📋 قائمة المستخدمين")
        table_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        table_title.setStyleSheet("color: #2c3e50; border: none; padding: 0; margin-bottom: 10px;")
        table_layout.addWidget(table_title)
        
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(4)
        self.users_table.setHorizontalHeaderLabels([
            "اسم المستخدم", "الاسم الكامل", "الدور", "تاريخ الإنشاء"
        ])
        
        # تعديل عرض الأعمدة
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        
        self.users_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.doubleClicked.connect(self.edit_user)
        self.users_table.setMinimumHeight(400)
        
        table_layout.addWidget(self.users_table)
        layout.addWidget(table_frame)
        
        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 10px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)
        
        # زر تغيير كلمة المرور
        change_password_button = QPushButton("🔒 تغيير كلمة المرور")
        change_password_button.setFixedHeight(35)
        change_password_button.clicked.connect(self.change_password)
        buttons_layout.addWidget(change_password_button)
        
        # أزرار المدير فقط
        if self.current_user['role'] == 'admin':
            edit_button = QPushButton("✏️ تعديل")
            edit_button.setFixedHeight(35)
            edit_button.clicked.connect(self.edit_user)
            buttons_layout.addWidget(edit_button)
            
            delete_button = QPushButton("🗑️ حذف")
            delete_button.setFixedHeight(35)
            delete_button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 13px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """)
            delete_button.clicked.connect(self.delete_user)
            buttons_layout.addWidget(delete_button)
        
        buttons_layout.addStretch()
        
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.setFixedHeight(35)
        refresh_button.clicked.connect(self.load_users)
        buttons_layout.addWidget(refresh_button)
        
        layout.addWidget(buttons_frame)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: none;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 13px;
            }
        """)
    
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            self.current_users = self.user_model.get_all_users()
            self.update_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المستخدمين: {str(e)}")
    
    def update_table(self):
        """تحديث جدول المستخدمين"""
        self.users_table.setRowCount(len(self.current_users))
        
        role_names = {"doctor": "طبيب", "secretary": "سكرتير", "admin": "مدير"}
        
        for row, user in enumerate(self.current_users):
            # اسم المستخدم
            self.users_table.setItem(row, 0, QTableWidgetItem(user['username']))
            
            # الاسم الكامل
            self.users_table.setItem(row, 1, QTableWidgetItem(user['full_name']))
            
            # الدور
            role_name = role_names.get(user['role'], user['role'])
            self.users_table.setItem(row, 2, QTableWidgetItem(role_name))
            
            # تاريخ الإنشاء
            from datetime import datetime
            created_date = datetime.strptime(user['created_at'], '%Y-%m-%d %H:%M:%S').strftime('%Y/%m/%d')
            self.users_table.setItem(row, 3, QTableWidgetItem(created_date))
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        if self.current_user['role'] != 'admin':
            QMessageBox.warning(self, "غير مسموح", "هذه العملية متاحة للمدير فقط")
            return
        
        dialog = UserDialog(self.user_model, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_users()
    
    def edit_user(self):
        """تعديل مستخدم"""
        if self.current_user['role'] != 'admin':
            QMessageBox.warning(self, "غير مسموح", "هذه العملية متاحة للمدير فقط")
            return
        
        current_row = self.users_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_users):
            user_data = self.current_users[current_row]
            dialog = UserDialog(self.user_model, user_data, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_users()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للتعديل")
    
    def delete_user(self):
        """حذف مستخدم"""
        if self.current_user['role'] != 'admin':
            QMessageBox.warning(self, "غير مسموح", "هذه العملية متاحة للمدير فقط")
            return
        
        current_row = self.users_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_users):
            user_data = self.current_users[current_row]
            
            # منع حذف المستخدم الحالي
            if user_data['id'] == self.current_user['id']:
                QMessageBox.warning(self, "غير مسموح", "لا يمكن حذف المستخدم الحالي")
                return
            
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف المستخدم:\n{user_data['full_name']}؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    success = self.user_model.delete_user(user_data['id'])
                    if success:
                        QMessageBox.information(self, "نجح", "تم حذف المستخدم بنجاح")
                        self.load_users()
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف المستخدم")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للحذف")
    
    def change_password(self):
        """تغيير كلمة المرور"""
        current_row = self.users_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_users):
            user_data = self.current_users[current_row]
            
            # التحقق من الصلاحيات
            if self.current_user['role'] != 'admin' and user_data['id'] != self.current_user['id']:
                QMessageBox.warning(self, "غير مسموح", "يمكنك تغيير كلمة مرورك فقط")
                return
            
            dialog = ChangePasswordDialog(self.user_model, user_data, parent=self)
            dialog.exec()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم لتغيير كلمة المرور")
