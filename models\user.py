import hashlib
import sqlite3
from database.database import DatabaseManager

class User:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def _hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        # للتوافق مع النظام الحالي، نتحقق من كلمة المرور بدون تشفير أولاً
        cursor.execute('''
            SELECT * FROM users 
            WHERE username = ? AND password = ?
        ''', (username, password))
        
        result = cursor.fetchone()
        
        # إذا لم نجد النتيجة، نجرب مع كلمة المرور المشفرة
        if not result:
            hashed_password = self._hash_password(password)
            cursor.execute('''
                SELECT * FROM users 
                WHERE username = ? AND password = ?
            ''', (username, hashed_password))
            result = cursor.fetchone()
        
        conn.close()
        return dict(result) if result else None
    
    def add_user(self, username, password, role, full_name):
        """إضافة مستخدم جديد"""
        if role not in ['doctor', 'secretary']:
            raise ValueError("الدور يجب أن يكون 'doctor' أو 'secretary'")
        
        hashed_password = self._hash_password(password)
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO users (username, password, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', (username, hashed_password, role, full_name))
            
            user_id = cursor.lastrowid
            conn.commit()
            return user_id
        except sqlite3.IntegrityError:
            raise Exception(f"اسم المستخدم '{username}' موجود مسبقاً")
        finally:
            conn.close()
    
    def get_user(self, user_id):
        """الحصول على بيانات مستخدم"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, username, role, full_name, created_at FROM users WHERE id = ?', (user_id,))
        result = cursor.fetchone()
        conn.close()
        return dict(result) if result else None
    
    def get_user_by_username(self, username):
        """الحصول على مستخدم باسم المستخدم"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, username, role, full_name, created_at FROM users WHERE username = ?', (username,))
        result = cursor.fetchone()
        conn.close()
        return dict(result) if result else None
    
    def update_user(self, user_id, **kwargs):
        """تحديث بيانات مستخدم"""
        if not kwargs:
            return False
        
        # تشفير كلمة المرور إذا تم تمريرها
        if 'password' in kwargs:
            kwargs['password'] = self._hash_password(kwargs['password'])
        
        # التحقق من صحة الدور
        if 'role' in kwargs and kwargs['role'] not in ['doctor', 'secretary']:
            raise ValueError("الدور يجب أن يكون 'doctor' أو 'secretary'")
        
        # بناء استعلام التحديث
        set_clause = ', '.join([f"{key} = ?" for key in kwargs.keys()])
        values = list(kwargs.values()) + [user_id]
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute(f'UPDATE users SET {set_clause} WHERE id = ?', values)
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        return affected_rows > 0
    
    def delete_user(self, user_id):
        """حذف مستخدم"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        return affected_rows > 0
    
    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, username, role, full_name, created_at FROM users ORDER BY full_name')
        results = cursor.fetchall()
        conn.close()
        return [dict(row) for row in results]
    
    def change_password(self, user_id, old_password, new_password):
        """تغيير كلمة المرور"""
        # التحقق من كلمة المرور القديمة
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        # جرب كلمة المرور بدون تشفير أولاً (للتوافق)
        cursor.execute('SELECT id FROM users WHERE id = ? AND password = ?', (user_id, old_password))
        result = cursor.fetchone()
        
        # إذا لم تنجح، جرب مع التشفير
        if not result:
            hashed_old_password = self._hash_password(old_password)
            cursor.execute('SELECT id FROM users WHERE id = ? AND password = ?', (user_id, hashed_old_password))
            result = cursor.fetchone()
        
        if not result:
            conn.close()
            raise Exception("كلمة المرور القديمة غير صحيحة")
        
        # تحديث كلمة المرور
        hashed_new_password = self._hash_password(new_password)
        cursor.execute('UPDATE users SET password = ? WHERE id = ?', (hashed_new_password, user_id))
        conn.commit()
        conn.close()
        
        return True
