from PyQt6.QtWidgets import (Q<PERSON><PERSON>og, QVBox<PERSON><PERSON>out, QHBox<PERSON>ayout, QLabel, 
                            QPushButton, QTextEdit, QFrame, QGroupBox,
                            QLineEdit, QComboBox, QSpinBox, QCheckBox,
                            QFileDialog, QMessageBox, QTabWidget, QWidget)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont, QPixmap, QTextDocument, QTextCursor
try:
    from PyQt6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
    PRINT_SUPPORT_AVAILABLE = True
except ImportError:
    PRINT_SUPPORT_AVAILABLE = False
    print("تحذير: مكتبة الطباعة غير متوفرة")
import os
from datetime import datetime
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

class PrescriptionPrintDialog(QDialog):
    """نافذة طباعة الوصفة الطبية"""
    
    def __init__(self, patient_data, visit_data, clinic_settings, parent=None):
        super().__init__(parent)
        self.patient_data = patient_data
        self.visit_data = visit_data
        self.clinic_settings = clinic_settings
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("طباعة الوصفة الطبية")
        self.setFixedSize(800, 700)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title_label = QLabel("🖨️ طباعة الوصفة الطبية")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب المحتوى
        content_tab = self.create_content_tab()
        tabs.addTab(content_tab, "📄 المحتوى")
        
        # تبويب التنسيق
        format_tab = self.create_format_tab()
        tabs.addTab(format_tab, "🎨 التنسيق")
        
        # تبويب المعاينة
        preview_tab = self.create_preview_tab()
        tabs.addTab(preview_tab, "👁️ المعاينة")
        
        layout.addWidget(tabs)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر إعدادات التصميم
        settings_button = QPushButton("⚙️ إعدادات التصميم")
        settings_button.setFixedHeight(40)
        settings_button.setFixedWidth(150)
        settings_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11px;
                padding: 8px 12px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        settings_button.clicked.connect(self.open_design_settings)
        buttons_layout.addWidget(settings_button)

        print_button = QPushButton("🖨️ طباعة")
        print_button.setFixedHeight(40)
        print_button.setFixedWidth(120)
        print_button.clicked.connect(self.print_prescription)
        buttons_layout.addWidget(print_button)
        
        pdf_button = QPushButton("📄 حفظ PDF")
        pdf_button.setFixedHeight(40)
        pdf_button.setFixedWidth(120)
        pdf_button.clicked.connect(self.save_as_pdf)
        buttons_layout.addWidget(pdf_button)

        # زر التقرير المحسن
        enhanced_button = QPushButton("📋 تقرير محسن")
        enhanced_button.setFixedHeight(40)
        enhanced_button.setFixedWidth(130)
        enhanced_button.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11px;
                padding: 8px 12px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        enhanced_button.clicked.connect(self.open_enhanced_report)
        buttons_layout.addWidget(enhanced_button)
        
        preview_button = QPushButton("👁️ معاينة الطباعة")
        preview_button.setFixedHeight(40)
        preview_button.setFixedWidth(150)
        preview_button.clicked.connect(self.show_print_preview)
        buttons_layout.addWidget(preview_button)
        
        # زر اختيار اللوغو
        logo_button = QPushButton("🖼️ اختيار لوغو العيادة")
        logo_button.setFixedHeight(40)
        logo_button.setFixedWidth(180)
        logo_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11px;
                padding: 8px 12px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        logo_button.clicked.connect(self.select_clinic_logo)
        buttons_layout.addWidget(logo_button)
        
        buttons_layout.addStretch()
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(40)
        cancel_button.setFixedWidth(100)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 1px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
    
    def create_content_tab(self):
        """إنشاء تبويب المحتوى"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # معلومات العيادة
        clinic_group = QGroupBox("🏥 معلومات العيادة")
        clinic_layout = QVBoxLayout(clinic_group)
        
        clinic_info_layout = QHBoxLayout()
        
        # العمود الأيسر
        left_layout = QVBoxLayout()
        
        clinic_name_label = QLabel("اسم العيادة:")
        clinic_name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        left_layout.addWidget(clinic_name_label)
        
        self.clinic_name_input = QLineEdit()
        self.clinic_name_input.setFixedHeight(35)
        left_layout.addWidget(self.clinic_name_input)
        
        doctor_name_label = QLabel("اسم الطبيب:")
        doctor_name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        left_layout.addWidget(doctor_name_label)
        
        self.doctor_name_input = QLineEdit()
        self.doctor_name_input.setFixedHeight(35)
        left_layout.addWidget(self.doctor_name_input)
        
        clinic_info_layout.addLayout(left_layout)
        
        # العمود الأيمن
        right_layout = QVBoxLayout()
        
        clinic_address_label = QLabel("عنوان العيادة:")
        clinic_address_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        right_layout.addWidget(clinic_address_label)
        
        self.clinic_address_input = QTextEdit()
        self.clinic_address_input.setFixedHeight(60)
        right_layout.addWidget(self.clinic_address_input)
        
        clinic_phone_label = QLabel("هاتف العيادة:")
        clinic_phone_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        right_layout.addWidget(clinic_phone_label)
        
        self.clinic_phone_input = QLineEdit()
        self.clinic_phone_input.setFixedHeight(35)
        right_layout.addWidget(self.clinic_phone_input)
        
        clinic_info_layout.addLayout(right_layout)
        clinic_layout.addLayout(clinic_info_layout)
        
        layout.addWidget(clinic_group)
        
        # معلومات المريض
        patient_group = QGroupBox("👤 معلومات المريض")
        patient_layout = QVBoxLayout(patient_group)
        
        patient_info = f"""
اسم المريض: {self.patient_data['full_name']}
رقم الملف: {self.patient_data['file_number']}
الهاتف: {self.patient_data.get('phone', 'غير محدد')}
العمر: {self.patient_data.get('age', 'غير محدد')} سنة
الجنس: {'ذكر' if self.patient_data.get('gender') == 'male' else 'أنثى' if self.patient_data.get('gender') == 'female' else 'غير محدد'}
        """.strip()
        
        patient_info_label = QLabel(patient_info)
        patient_info_label.setStyleSheet("""
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            color: #495057;
        """)
        patient_layout.addWidget(patient_info_label)
        
        layout.addWidget(patient_group)
        
        # وصف العلاج
        treatment_group = QGroupBox("💊 وصف العلاج")
        treatment_layout = QVBoxLayout(treatment_group)
        
        self.treatment_text = QTextEdit()
        self.treatment_text.setMinimumHeight(200)
        self.treatment_text.setPlainText(self.visit_data.get('treatment_description', ''))
        treatment_layout.addWidget(self.treatment_text)
        
        layout.addWidget(treatment_group)
        
        return widget
    
    def create_format_tab(self):
        """إنشاء تبويب التنسيق"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # إعدادات الخط
        font_group = QGroupBox("🔤 إعدادات الخط")
        font_layout = QVBoxLayout(font_group)
        
        font_settings_layout = QHBoxLayout()
        
        # حجم الخط
        font_size_label = QLabel("حجم الخط:")
        font_settings_layout.addWidget(font_size_label)
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(12)
        font_settings_layout.addWidget(self.font_size_spin)
        
        font_settings_layout.addStretch()
        
        font_layout.addLayout(font_settings_layout)
        layout.addWidget(font_group)
        
        # إعدادات الصفحة
        page_group = QGroupBox("📄 إعدادات الصفحة")
        page_layout = QVBoxLayout(page_group)
        
        page_settings_layout = QHBoxLayout()
        
        # حجم الصفحة
        page_size_label = QLabel("حجم الصفحة:")
        page_settings_layout.addWidget(page_size_label)
        
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["A4", "Letter"])
        page_settings_layout.addWidget(self.page_size_combo)
        
        page_settings_layout.addStretch()
        
        # الهوامش
        margins_label = QLabel("الهوامش (سم):")
        page_settings_layout.addWidget(margins_label)
        
        self.margins_spin = QSpinBox()
        self.margins_spin.setRange(1, 5)
        self.margins_spin.setValue(2)
        page_settings_layout.addWidget(self.margins_spin)
        
        page_layout.addLayout(page_settings_layout)
        layout.addWidget(page_group)
        
        # خيارات إضافية
        options_group = QGroupBox("⚙️ خيارات إضافية")
        options_layout = QVBoxLayout(options_group)
        
        self.include_logo_checkbox = QCheckBox("تضمين شعار العيادة")
        self.include_logo_checkbox.setChecked(True)
        options_layout.addWidget(self.include_logo_checkbox)
        
        self.include_date_checkbox = QCheckBox("تضمين التاريخ والوقت")
        self.include_date_checkbox.setChecked(True)
        options_layout.addWidget(self.include_date_checkbox)
        
        self.include_signature_checkbox = QCheckBox("تضمين مساحة للتوقيع")
        self.include_signature_checkbox.setChecked(True)
        options_layout.addWidget(self.include_signature_checkbox)
        
        layout.addWidget(options_group)
        
        # إعدادات اللوغو
        logo_group = QGroupBox("🖼️ إعدادات اللوغو")
        logo_layout = QVBoxLayout(logo_group)
        
        # خيار إظهار اللوغو
        self.show_logo_checkbox = QCheckBox("إظهار لوغو العيادة")
        settings = self.load_custom_settings()
        self.show_logo_checkbox.setChecked(settings.get('show_logo', True))
        self.show_logo_checkbox.stateChanged.connect(self.update_preview)
        logo_layout.addWidget(self.show_logo_checkbox)
        
        # أبعاد اللوغو
        logo_size_layout = QHBoxLayout()
        
        logo_width_label = QLabel("عرض اللوغو:")
        logo_size_layout.addWidget(logo_width_label)
        
        self.logo_width_spinbox = QSpinBox()
        self.logo_width_spinbox.setRange(50, 300)
        self.logo_width_spinbox.setValue(settings.get('logo_width', 120))
        self.logo_width_spinbox.setSuffix(" بكسل")
        self.logo_width_spinbox.valueChanged.connect(self.update_preview)
        logo_size_layout.addWidget(self.logo_width_spinbox)
        
        logo_height_label = QLabel("ارتفاع اللوغو:")
        logo_size_layout.addWidget(logo_height_label)
        
        self.logo_height_spinbox = QSpinBox()
        self.logo_height_spinbox.setRange(50, 300)
        self.logo_height_spinbox.setValue(settings.get('logo_height', 120))
        self.logo_height_spinbox.setSuffix(" بكسل")
        self.logo_height_spinbox.valueChanged.connect(self.update_preview)
        logo_size_layout.addWidget(self.logo_height_spinbox)
        
        logo_layout.addLayout(logo_size_layout)
        
        # إعدادات الألوان
        colors_group = QGroupBox("🎨 إعدادات الألوان")
        colors_layout = QVBoxLayout(colors_group)
        
        # لون الرأس
        header_color_layout = QHBoxLayout()
        header_color_label = QLabel("لون رأس الصفحة:")
        header_color_layout.addWidget(header_color_label)
        
        self.header_color_button = QPushButton()
        self.header_color_button.setFixedSize(50, 30)
        self.header_color_button.setStyleSheet(f"background-color: #2E8B57; border: 1px solid #ccc; border-radius: 4px;")
        self.header_color_button.clicked.connect(lambda: self.choose_color('header'))
        header_color_layout.addWidget(self.header_color_button)
        header_color_layout.addStretch()
        
        colors_layout.addLayout(header_color_layout)
        
        # لون الوصفة
        prescription_color_layout = QHBoxLayout()
        prescription_color_label = QLabel("لون الوصفة الطبية:")
        prescription_color_layout.addWidget(prescription_color_label)
        
        self.prescription_color_button = QPushButton()
        self.prescription_color_button.setFixedSize(50, 30)
        self.prescription_color_button.setStyleSheet(f"background-color: #EF4444; border: 1px solid #ccc; border-radius: 4px;")
        self.prescription_color_button.clicked.connect(lambda: self.choose_color('prescription'))
        prescription_color_layout.addWidget(self.prescription_color_button)
        prescription_color_layout.addStretch()
        
        colors_layout.addLayout(prescription_color_layout)
        
        layout.addWidget(logo_group)
        layout.addWidget(colors_group)
        
        return widget
    
    def create_preview_tab(self):
        """إنشاء تبويب المعاينة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        preview_label = QLabel("معاينة الوصفة:")
        preview_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(preview_label)
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setMinimumHeight(500)
        layout.addWidget(self.preview_text)
        
        # زر تحديث المعاينة
        update_preview_button = QPushButton("🔄 تحديث المعاينة")
        update_preview_button.clicked.connect(self.update_preview)
        layout.addWidget(update_preview_button)
        
        return widget
    
    def load_settings(self):
        """تحميل الإعدادات من قاعدة البيانات"""
        self.clinic_name_input.setText(self.clinic_settings.get('clinic_name', ''))
        self.doctor_name_input.setText(self.clinic_settings.get('doctor_name', ''))
        self.clinic_address_input.setPlainText(self.clinic_settings.get('clinic_address', ''))
        self.clinic_phone_input.setText(self.clinic_settings.get('clinic_phone', ''))
        
        # تحديث المعاينة
        self.update_preview()
    
    def update_preview(self):
        """تحديث معاينة الوصفة"""
        preview_html = self.generate_prescription_html()
        self.preview_text.setHtml(preview_html)
    
    def load_custom_settings(self):
        """تحميل الإعدادات المخصصة"""
        try:
            import json
            import os

            settings_file = "prescription_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass

        # الإعدادات الافتراضية - تصميم عصري ومضغوط للطباعة على A4
        return {
            "base_font_size": 11,
            "clinic_name_size": 18,
            "doctor_name_size": 14,
            "prescription_size": 13,
            "medication_size": 12,
            "font_family": "Arial",
            "header_color": "#2c3e50",
            "patient_color": "#27ae60",
            "diagnosis_color": "#e67e22",
            "prescription_color": "#e74c3c",
            "page_margins": 12,
            "section_spacing": 12,
            "page_size": "A4",
            "show_header": True,
            "show_patient_info": True,
            "show_diagnosis": True,
            "show_prescription": True,
            "show_signature": True,
            "show_footer": True,
            "footer_text": "🏥 تم إنشاء هذه الوصفة بواسطة نظام إدارة العيادة الطبية",
            "signature_text": "توقيع الطبيب المعالج"
        }

    def generate_prescription_html(self):
        """إنشاء HTML للوصفة الطبية مطابق للتصميم في الصورة"""
        # تحميل الإعدادات المخصصة
        settings = self.load_custom_settings()

        clinic_name = self.clinic_name_input.text() or "عيادة طبية"
        doctor_name = self.doctor_name_input.text() or "د. طبيب"
        clinic_address = settings.get('clinic_address', 'العنوان: شارع الطب، مدينة الرياض')
        clinic_phone = settings.get('clinic_phone', 'هاتف: **********')
        clinic_mobile = settings.get('clinic_mobile', 'جوال: 0501234567')
        clinic_email = settings.get('clinic_email', '<EMAIL>')

        current_date = datetime.now().strftime("%Y/%m/%d")
        current_time = datetime.now().strftime("%H:%M")

        treatment_text = self.treatment_text.toPlainText() or "لا يوجد وصف علاج"

        # الحصول على التشخيص إذا كان متوفراً
        diagnosis = ""
        if hasattr(self, 'visit_data') and self.visit_data:
            diagnosis = self.visit_data.get('diagnosis', '')

        # تنسيق الوصفة للطباعة
        formatted_treatment = self.format_treatment_for_print(treatment_text, settings)

        # إعداد اللوغو
        logo_section = ""
        if settings.get('show_logo', True) and settings.get('logo_path'):
            logo_section = f'''
            <div class="logo-container">
                <img src="{settings['logo_path']}" alt="شعار العيادة" class="clinic-logo">
            </div>
            '''
        else:
            # لوغو افتراضي إذا لم يتم تحديد لوغو
            logo_section = '''
            <div class="logo-container">
                <div class="default-logo">
                    <div class="logo-icon">🏥</div>
                    <div class="logo-text">عيادة طبية</div>
                </div>
            </div>
            '''

        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>وصفة طبية - {self.patient_data['full_name']}</title>
            <style>
                @page {{
                    size: A4;
                    margin: {settings['page_margins']}mm;
                }}
                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}
                body {{
                    font-family: '{settings['font_family']}', 'Tahoma', sans-serif;
                    font-size: {settings['base_font_size']}px;
                    line-height: 1.6;
                    color: #1E293B;
                    background: {settings.get('background_color', '#ffffff')};
                    direction: rtl;
                    padding: 0;
                }}
                
                /* تصميم مطابق للصورة المرفقة */
                .prescription-container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    border: 2px solid {settings.get('border_color', '#e2e8f0')};
                    border-radius: 15px;
                    overflow: hidden;
                }}
                
                /* Header مع اللوغو - مطابق للصورة */
                .header {{
                    background: linear-gradient(135deg, {settings['header_color']} 0%, {self.darken_color(settings['header_color'])} 100%);
                    color: white;
                    padding: 20px;
                    text-align: center;
                    position: relative;
                }}
                
                .logo-container {{
                    margin-bottom: 15px;
                }}
                
                .clinic-logo {{
                    width: {settings.get('logo_width', 120)}px;
                    height: {settings.get('logo_height', 120)}px;
                    border-radius: 50%;
                    border: 3px solid white;
                    background: white;
                    padding: 5px;
                }}
                
                .default-logo {{
                    width: {settings.get('logo_width', 120)}px;
                    height: {settings.get('logo_height', 120)}px;
                    background: white;
                    border-radius: 50%;
                    border: 3px solid white;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto;
                }}
                
                .logo-icon {{
                    font-size: 40px;
                    color: #2E8B57;
                }}
                
                .logo-text {{
                    font-size: 14px;
                    color: #2E8B57;
                    font-weight: bold;
                    margin-top: 5px;
                }}
                
                .clinic-name {{
                    font-size: {settings['clinic_name_size']}px;
                    font-weight: 700;
                    margin: 10px 0;
                    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
                }}
                
                .doctor-name {{
                    font-size: {settings['doctor_name_size']}px;
                    font-weight: 600;
                    margin: 8px 0;
                    opacity: 0.95;
                }}
                
                .contact-info {{
                    font-size: {settings['base_font_size'] - 1}px;
                    opacity: 0.9;
                    line-height: 1.4;
                    margin-top: 10px;
                }}
                
                /* معلومات المريض والتاريخ - تحت الهيدر مباشرة */
                .patient-date-section {{
                    background: #F8FAFC;
                    padding: 20px;
                    border-bottom: 2px solid #E2E8F0;
                }}
                
                .visit-date {{
                    text-align: center;
                    background: #2E8B57;
                    color: white;
                    padding: 10px;
                    border-radius: 8px;
                    font-weight: 600;
                    margin-bottom: 15px;
                    font-size: {settings['base_font_size'] + 1}px;
                }}
                
                .patient-info {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 15px;
                    background: white;
                    padding: 15px;
                    border-radius: 8px;
                    border: 1px solid #E2E8F0;
                }}
                
                .patient-field {{
                    display: flex;
                    align-items: center;
                    padding: 8px;
                    border-right: 3px solid #2E8B57;
                    background: #F8FAFC;
                    border-radius: 4px;
                }}
                
                .field-label {{
                    font-weight: 600;
                    color: #2E8B57;
                    margin-left: 8px;
                    min-width: 60px;
                }}
                
                .field-value {{
                    color: #1E293B;
                    font-weight: 500;
                }}
                
                /* التشخيص */
                .diagnosis-section {{
                    background: #FFF7ED;
                    border: 2px solid #FB923C;
                    margin: 20px;
                    border-radius: 8px;
                    overflow: hidden;
                }}
                
                .diagnosis-header {{
                    background: #FB923C;
                    color: white;
                    padding: 12px;
                    text-align: center;
                    font-weight: 600;
                    font-size: {settings['base_font_size'] + 1}px;
                }}
                
                .diagnosis-content {{
                    padding: 15px;
                    background: white;
                    font-size: {settings['base_font_size']}px;
                    line-height: 1.6;
                    min-height: 60px;
                    border-right: 4px solid #FB923C;
                }}
                
                /* الوصفة الطبية - التركيز الرئيسي */
                .treatment-section {{
                    background: #FEF2F2;
                    border: 2px solid #EF4444;
                    margin: 20px;
                    border-radius: 8px;
                    overflow: hidden;
                }}
                
                .treatment-header {{
                    background: linear-gradient(135deg, {settings['prescription_color']} 0%, {self.darken_color(settings['prescription_color'])} 100%);
                    color: white;
                    padding: 15px;
                    text-align: center;
                    font-weight: 600;
                    font-size: {settings['prescription_size']}px;
                }}
                
                .treatment-content {{
                    padding: 20px;
                    background: white;
                }}
                
                .medication-item {{
                    background: white;
                    padding: 15px;
                    margin: 10px 0;
                    border-radius: 8px;
                    border-right: 4px solid #EF4444;
                    font-size: {settings['medication_size']}px;
                    line-height: 1.5;
                    position: relative;
                    padding-right: 50px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    border: 1px solid #FEE2E2;
                }}
                
                .medication-item::before {{
                    content: '💊';
                    position: absolute;
                    right: 15px;
                    top: 50%;
                    transform: translateY(-50%);
                    font-size: 20px;
                }}
                
                .medication-name {{
                    font-weight: 600;
                    color: #1E293B;
                    margin-bottom: 5px;
                }}
                
                .medication-dose {{
                    color: #64748B;
                    font-size: {settings['medication_size'] - 1}px;
                }}
                
                /* الفوتر مع معلومات العيادة - مطابق للصورة */
                .footer {{
                    background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
                    color: white;
                    padding: 20px;
                    margin-top: 30px;
                }}
                
                .footer-content {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 30px;
                    align-items: center;
                }}
                
                .signature-area {{
                    text-align: center;
                }}
                
                .signature-line {{
                    height: 60px;
                    border: 2px dashed white;
                    border-radius: 8px;
                    margin: 10px 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: {settings['base_font_size'] - 1}px;
                    opacity: 0.8;
                }}
                
                .contact-details {{
                    font-size: {settings['base_font_size'] - 1}px;
                    line-height: 1.8;
                }}
                
                .contact-item {{
                    margin: 5px 0;
                    display: flex;
                    align-items: center;
                }}
                
                .contact-icon {{
                    margin-left: 8px;
                    font-size: 16px;
                }}
                
                /* إضافات للطباعة */
                @media print {{
                    body {{
                        font-size: {int(settings['base_font_size'] * 1.1)}px;
                        print-color-adjust: exact;
                        -webkit-print-color-adjust: exact;
                    }}
                    .medication-item {{
                        font-size: {int(settings['medication_size'] * 1.1)}px;
                        page-break-inside: avoid;
                    }}
                    .prescription-container {{
                        page-break-inside: avoid;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="prescription-container">
                <!-- Header مع اللوغو -->
                <div class="header">
                    {logo_section}
                    <div class="clinic-name">{clinic_name}</div>
                    <div class="doctor-name">{doctor_name}</div>
                    <div class="contact-info">
                        📍 {clinic_address}<br>
                        📞 {clinic_phone} | 📱 {clinic_mobile}
                    </div>
                </div>

                <!-- معلومات المريض والتاريخ -->
                <div class="patient-date-section">
                    <div class="visit-date">
                        📅 تاريخ المراجعة: {current_date} - {current_time}
                    </div>
                    <div class="patient-info">
                        <div class="patient-field">
                            <span class="field-label">الاسم:</span>
                            <span class="field-value">{self.patient_data['full_name']}</span>
                        </div>
                        <div class="patient-field">
                            <span class="field-label">العمر:</span>
                            <span class="field-value">{self.patient_data.get('age', 'غير محدد')} سنة</span>
                        </div>
                        <div class="patient-field">
                            <span class="field-label">رقم الملف:</span>
                            <span class="field-value">{self.patient_data.get('file_number', 'غير محدد')}</span>
                        </div>
                        <div class="patient-field">
                            <span class="field-label">الجنس:</span>
                            <span class="field-value">{'ذكر' if self.patient_data.get('gender') == 'male' else 'أنثى'}</span>
                        </div>
                    </div>
                </div>

                <!-- التشخيص (إذا كان متوفراً) -->
                {f'''
                <div class="diagnosis-section">
                    <div class="diagnosis-header">🔍 التشخيص</div>
                    <div class="diagnosis-content">{diagnosis}</div>
                </div>
                ''' if diagnosis else ''}

                <!-- الوصفة الطبية -->
                <div class="treatment-section">
                    <div class="treatment-header">💊 الوصفة الطبية والعلاج</div>
                    <div class="treatment-content">
                        {formatted_treatment}
                    </div>
                </div>

                <!-- الفوتر مع التوقيع ومعلومات العيادة -->
                <div class="footer">
                    <div class="footer-content">
                        <div class="signature-area">
                            <div style="font-weight: 600; margin-bottom: 10px;">توقيع الطبيب المعالج</div>
                            <div class="signature-line">د. {doctor_name}</div>
                        </div>
                        <div class="contact-details">
                            <div class="contact-item">
                                <span class="contact-icon">📍</span>
                                <span>{clinic_address}</span>
                            </div>
                            <div class="contact-item">
                                <span class="contact-icon">📞</span>
                                <span>{clinic_phone}</span>
                            </div>
                            <div class="contact-item">
                                <span class="contact-icon">📱</span>
                                <span>{clinic_mobile}</span>
                            </div>
                            <div class="contact-item">
                                <span class="contact-icon">📧</span>
                                <span>{clinic_email}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """

        return html

    def format_treatment_for_print(self, treatment, settings=None):
        """تنسيق الوصفة للطباعة مع التصميم العصري"""
        if settings is None:
            settings = self.load_custom_settings()

        if not treatment:
            return '''
            <div class="medication-item" style="text-align: center; color: #64748B; font-style: italic;">
                <div style="padding: 20px; border: 2px dashed #E2E8F0; border-radius: 8px;">
                    لا توجد وصفة طبية محددة
                </div>
            </div>
            '''

        lines = treatment.split('\n')
        formatted_lines = []
        medication_counter = 1

        for line in lines:
            line = line.strip()
            if line:
                # تنظيف السطر وتحسين التنسيق
                cleaned_line = line

                # إذا كان السطر يبدأ برقم، استخدمه كما هو
                if line[0].isdigit():
                    formatted_lines.append(f'''
                    <div class="medication-item">
                        <div class="medication-name">{cleaned_line.split('.', 1)[1].strip() if '.' in cleaned_line else cleaned_line}</div>
                    </div>
                    ''')
                # إذا كان يبدأ بـ - أو •، استبدلها برقم
                elif line.startswith('-') or line.startswith('•'):
                    content = line[1:].strip()
                    # تقسيم الدواء والجرعة
                    parts = content.split(' - ') if ' - ' in content else [content]
                    main_part = parts[0]
                    dose_part = parts[1] if len(parts) > 1 else ""
                    
                    formatted_lines.append(f'''
                    <div class="medication-item">
                        <div class="medication-name">{medication_counter}. {main_part}</div>
                        {f'<div class="medication-dose">{dose_part}</div>' if dose_part else ''}
                    </div>
                    ''')
                    medication_counter += 1
                else:
                    # إذا لم يكن مرقم، أضف ترقيم
                    if not any(char.isdigit() for char in line[:3]):
                        # تقسيم الدواء والجرعة
                        parts = line.split(' - ') if ' - ' in line else [line]
                        main_part = parts[0]
                        dose_part = parts[1] if len(parts) > 1 else ""
                        
                        formatted_lines.append(f'''
                        <div class="medication-item">
                            <div class="medication-name">{medication_counter}. {main_part}</div>
                            {f'<div class="medication-dose">{dose_part}</div>' if dose_part else ''}
                        </div>
                        ''')
                        medication_counter += 1
                    else:
                        formatted_lines.append(f'''
                        <div class="medication-item">
                            <div class="medication-name">{cleaned_line}</div>
                        </div>
                        ''')

        if not formatted_lines:
            return '''
            <div class="medication-item" style="text-align: center; color: #64748B; font-style: italic;">
                <div style="padding: 20px; border: 2px dashed #E2E8F0; border-radius: 8px;">
                    لا توجد وصفة طبية محددة
                </div>
            </div>
            '''

        return '\n'.join(formatted_lines)

    def darken_color(self, color):
        """تغميق اللون للتدرج"""
        color_map = {
            "#3498db": "#2980b9",
            "#e74c3c": "#c0392b",
            "#27ae60": "#229954",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad",
            "#1abc9c": "#16a085",
            "#ff9800": "#f57c00"
        }
        return color_map.get(color, color)

    def print_prescription(self):
        """طباعة الوصفة"""
        if not PRINT_SUPPORT_AVAILABLE:
            QMessageBox.warning(self, "خطأ", "مكتبة الطباعة غير متوفرة")
            return

        try:
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            self.setup_printer_page_size(printer)
            
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec() == QPrintDialog.DialogCode.Accepted:
                document = QTextDocument()
                document.setHtml(self.generate_prescription_html())
                document.print(printer)
                
                QMessageBox.information(self, "نجح", "تم طباعة الوصفة بنجاح")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الطباعة: {str(e)}")
    
    def save_as_pdf(self):
        """حفظ الوصفة كملف PDF"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ الوصفة كـ PDF", 
                f"وصفة_{self.patient_data['full_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF files (*.pdf)"
            )
            
            if file_path:
                printer = QPrinter(QPrinter.PrinterMode.HighResolution)
                printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)
                printer.setOutputFileName(file_path)
                self.setup_printer_page_size(printer)
                
                document = QTextDocument()
                document.setHtml(self.generate_prescription_html())
                document.print(printer)
                
                QMessageBox.information(self, "نجح", f"تم حفظ الوصفة في:\n{file_path}")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ PDF: {str(e)}")
    
    def show_print_preview(self):
        """عرض معاينة الطباعة"""
        if not PRINT_SUPPORT_AVAILABLE:
            QMessageBox.warning(self, "خطأ", "مكتبة الطباعة غير متوفرة")
            return

        try:
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            self.setup_printer_page_size(printer)
            
            preview_dialog = QPrintPreviewDialog(printer, self)
            preview_dialog.paintRequested.connect(self.print_preview)
            preview_dialog.exec()
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عرض المعاينة: {str(e)}")

    def setup_printer_page_size(self, printer):
        """إعداد حجم الصفحة للطابعة مع إعدادات محسنة"""
        try:
            # إعداد جودة الطباعة
            printer.setResolution(300)  # DPI عالي للحصول على جودة أفضل
            
            # إعداد حجم الصفحة
            from PyQt6.QtGui import QPageSize
            page_size = QPageSize(QPageSize.PageSizeId.A4)
            printer.setPageSize(page_size)
            
            # إعداد الهوامش
            from PyQt6.QtCore import QMarginsF
            margins = QMarginsF(20, 20, 20, 20)  # هوامش بالملم
            printer.setPageMargins(margins, QPageSize.Unit.Millimeter)
            
        except ImportError:
            try:
                # محاولة استخدام الطريقة القديمة
                printer.setPageSize(QPrinter.PageSize.A4)
                printer.setResolution(300)
            except AttributeError:
                try:
                    # محاولة أخرى مع enum
                    from PyQt6.QtPrintSupport import QPrinter
                    printer.setPageSize(QPrinter.A4)
                    printer.setResolution(300)
                except:
                    # إعداد افتراضي
                    print("تحذير: لا يمكن تعيين حجم الصفحة")
        except Exception as e:
            print(f"خطأ في إعداد حجم الصفحة: {e}")
    
    def print_preview(self, printer):
        """طباعة المعاينة مع إعدادات محسنة"""
        document = QTextDocument()
        html_content = self.generate_prescription_html()
        document.setHtml(html_content)
        
        # إعداد حجم المستند ليتناسب مع A4
        from PyQt6.QtCore import QSizeF
        document.setPageSize(QSizeF(595, 842))  # A4 بالنقاط
        
        document.print(printer)

    def open_design_settings(self):
        """فتح نافذة إعدادات التصميم"""
        try:
            from ui.prescription_settings_widget import PrescriptionSettingsDialog

            settings_dialog = PrescriptionSettingsDialog(self)
            settings_dialog.settings_changed.connect(self.apply_design_settings)
            settings_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إعدادات التصميم: {str(e)}")

    def apply_design_settings(self, settings):
        """تطبيق إعدادات التصميم الجديدة"""
        try:
            # حفظ الإعدادات الجديدة
            import json
            with open("prescription_settings.json", 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            # تحديث المعاينة بالإعدادات الجديدة
            self.update_preview()
            QMessageBox.information(self, "نجح", "تم تطبيق وحفظ الإعدادات الجديدة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تطبيق الإعدادات: {str(e)}")

    def open_enhanced_report(self):
        """فتح التقرير المحسن"""
        try:
            from utils.enhanced_print_report import EnhancedPrintReportDialog

            # الحصول على النص من التبويب الحالي
            treatment_text = ""
            if hasattr(self, 'treatment_text'):
                treatment_text = self.treatment_text.toPlainText()

            enhanced_dialog = EnhancedPrintReportDialog(
                self.patient_data,
                self.visit_data if hasattr(self, 'visit_data') else {},
                treatment_text,
                self
            )
            enhanced_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح التقرير المحسن: {str(e)}")

    def save_custom_settings(self, settings):
        """حفظ الإعدادات المخصصة"""
        try:
            import json
            
            settings_file = "prescription_settings.json"
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")

    def apply_format_settings(self):
        """تطبيق إعدادات التنسيق"""
        settings = self.load_custom_settings()
        
        # تحديث إعدادات اللوغو
        if hasattr(self, 'show_logo_checkbox'):
            settings['show_logo'] = self.show_logo_checkbox.isChecked()
        if hasattr(self, 'logo_width_spinbox'):
            settings['logo_width'] = self.logo_width_spinbox.value()
        if hasattr(self, 'logo_height_spinbox'):
            settings['logo_height'] = self.logo_height_spinbox.value()
        
        # حفظ الإعدادات
        self.save_custom_settings(settings)
        
        # تحديث المعاينة
        self.update_preview()

    def select_clinic_logo(self):
        """اختيار لوغو العيادة"""
        from PyQt6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار لوغو العيادة",
            "",
            "ملفات الصور (*.png *.jpg *.jpeg *.gif *.bmp);;جميع الملفات (*)"
        )
        
        if file_path:
            # حفظ مسار اللوغو في الإعدادات
            settings = self.load_custom_settings()
            settings['logo_path'] = file_path
            settings['show_logo'] = True
            self.save_custom_settings(settings)
            
            # تحديث المعاينة
            self.update_preview()
            
            QMessageBox.information(
                self,
                "تم الحفظ",
                f"تم حفظ لوغو العيادة بنجاح:\n{file_path}"
            )
    
    def choose_color(self, color_type):
        """اختيار لون مخصص"""
        from PyQt6.QtWidgets import QColorDialog
        from PyQt6.QtGui import QColor
        
        current_settings = self.load_custom_settings()
        
        if color_type == 'header':
            current_color = QColor("#2E8B57")
        elif color_type == 'prescription':
            current_color = QColor("#EF4444")
        else:
            current_color = QColor("#2E8B57")
        
        color = QColorDialog.getColor(current_color, self, f"اختيار لون {color_type}")
        
        if color.isValid():
            color_hex = color.name()
            
            # تحديث زر اللون
            if color_type == 'header':
                self.header_color_button.setStyleSheet(f"background-color: {color_hex}; border: 1px solid #ccc; border-radius: 4px;")
                current_settings['header_color'] = color_hex
            elif color_type == 'prescription':
                self.prescription_color_button.setStyleSheet(f"background-color: {color_hex}; border: 1px solid #ccc; border-radius: 4px;")
                current_settings['prescription_color'] = color_hex
            
            # حفظ الإعدادات
            self.save_custom_settings(current_settings)
            
            # تحديث المعاينة
            self.update_preview()
