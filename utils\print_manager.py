from PyQt6.QtWidgets import (Q<PERSON><PERSON>og, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QFrame, QGroupBox,
                            QLineEdit, QComboBox, QSpinBox, QCheckBox,
                            QFileDialog, QMessageBox, QTabWidget, QWidget)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont, QPixmap, QTextDocument, QTextCursor
from PyQt6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import <PERSON><PERSON>ont
import os
from datetime import datetime
import arabic_reshaper
from bidi.algorithm import get_display

class PrescriptionPrintDialog(QDialog):
    """نافذة طباعة الوصفة الطبية"""
    
    def __init__(self, patient_data, visit_data, clinic_settings, parent=None):
        super().__init__(parent)
        self.patient_data = patient_data
        self.visit_data = visit_data
        self.clinic_settings = clinic_settings
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("طباعة الوصفة الطبية")
        self.setFixedSize(800, 700)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title_label = QLabel("🖨️ طباعة الوصفة الطبية")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب المحتوى
        content_tab = self.create_content_tab()
        tabs.addTab(content_tab, "📄 المحتوى")
        
        # تبويب التنسيق
        format_tab = self.create_format_tab()
        tabs.addTab(format_tab, "🎨 التنسيق")
        
        # تبويب المعاينة
        preview_tab = self.create_preview_tab()
        tabs.addTab(preview_tab, "👁️ المعاينة")
        
        layout.addWidget(tabs)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        print_button = QPushButton("🖨️ طباعة")
        print_button.setFixedHeight(40)
        print_button.setFixedWidth(120)
        print_button.clicked.connect(self.print_prescription)
        buttons_layout.addWidget(print_button)
        
        pdf_button = QPushButton("📄 حفظ PDF")
        pdf_button.setFixedHeight(40)
        pdf_button.setFixedWidth(120)
        pdf_button.clicked.connect(self.save_as_pdf)
        buttons_layout.addWidget(pdf_button)
        
        preview_button = QPushButton("👁️ معاينة الطباعة")
        preview_button.setFixedHeight(40)
        preview_button.setFixedWidth(150)
        preview_button.clicked.connect(self.show_print_preview)
        buttons_layout.addWidget(preview_button)
        
        buttons_layout.addStretch()
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(40)
        cancel_button.setFixedWidth(100)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 1px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
    
    def create_content_tab(self):
        """إنشاء تبويب المحتوى"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # معلومات العيادة
        clinic_group = QGroupBox("🏥 معلومات العيادة")
        clinic_layout = QVBoxLayout(clinic_group)
        
        clinic_info_layout = QHBoxLayout()
        
        # العمود الأيسر
        left_layout = QVBoxLayout()
        
        clinic_name_label = QLabel("اسم العيادة:")
        clinic_name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        left_layout.addWidget(clinic_name_label)
        
        self.clinic_name_input = QLineEdit()
        self.clinic_name_input.setFixedHeight(35)
        left_layout.addWidget(self.clinic_name_input)
        
        doctor_name_label = QLabel("اسم الطبيب:")
        doctor_name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        left_layout.addWidget(doctor_name_label)
        
        self.doctor_name_input = QLineEdit()
        self.doctor_name_input.setFixedHeight(35)
        left_layout.addWidget(self.doctor_name_input)
        
        clinic_info_layout.addLayout(left_layout)
        
        # العمود الأيمن
        right_layout = QVBoxLayout()
        
        clinic_address_label = QLabel("عنوان العيادة:")
        clinic_address_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        right_layout.addWidget(clinic_address_label)
        
        self.clinic_address_input = QTextEdit()
        self.clinic_address_input.setFixedHeight(60)
        right_layout.addWidget(self.clinic_address_input)
        
        clinic_phone_label = QLabel("هاتف العيادة:")
        clinic_phone_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        right_layout.addWidget(clinic_phone_label)
        
        self.clinic_phone_input = QLineEdit()
        self.clinic_phone_input.setFixedHeight(35)
        right_layout.addWidget(self.clinic_phone_input)
        
        clinic_info_layout.addLayout(right_layout)
        clinic_layout.addLayout(clinic_info_layout)
        
        layout.addWidget(clinic_group)
        
        # معلومات المريض
        patient_group = QGroupBox("👤 معلومات المريض")
        patient_layout = QVBoxLayout(patient_group)
        
        patient_info = f"""
اسم المريض: {self.patient_data['full_name']}
رقم الملف: {self.patient_data['file_number']}
الهاتف: {self.patient_data.get('phone', 'غير محدد')}
العمر: {self.patient_data.get('age', 'غير محدد')} سنة
الجنس: {'ذكر' if self.patient_data.get('gender') == 'male' else 'أنثى' if self.patient_data.get('gender') == 'female' else 'غير محدد'}
        """.strip()
        
        patient_info_label = QLabel(patient_info)
        patient_info_label.setStyleSheet("""
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            color: #495057;
        """)
        patient_layout.addWidget(patient_info_label)
        
        layout.addWidget(patient_group)
        
        # وصف العلاج
        treatment_group = QGroupBox("💊 وصف العلاج")
        treatment_layout = QVBoxLayout(treatment_group)
        
        self.treatment_text = QTextEdit()
        self.treatment_text.setMinimumHeight(200)
        self.treatment_text.setPlainText(self.visit_data.get('treatment_description', ''))
        treatment_layout.addWidget(self.treatment_text)
        
        layout.addWidget(treatment_group)
        
        return widget
    
    def create_format_tab(self):
        """إنشاء تبويب التنسيق"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # إعدادات الخط
        font_group = QGroupBox("🔤 إعدادات الخط")
        font_layout = QVBoxLayout(font_group)
        
        font_settings_layout = QHBoxLayout()
        
        # حجم الخط
        font_size_label = QLabel("حجم الخط:")
        font_settings_layout.addWidget(font_size_label)
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(12)
        font_settings_layout.addWidget(self.font_size_spin)
        
        font_settings_layout.addStretch()
        
        font_layout.addLayout(font_settings_layout)
        layout.addWidget(font_group)
        
        # إعدادات الصفحة
        page_group = QGroupBox("📄 إعدادات الصفحة")
        page_layout = QVBoxLayout(page_group)
        
        page_settings_layout = QHBoxLayout()
        
        # حجم الصفحة
        page_size_label = QLabel("حجم الصفحة:")
        page_settings_layout.addWidget(page_size_label)
        
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["A4", "Letter"])
        page_settings_layout.addWidget(self.page_size_combo)
        
        page_settings_layout.addStretch()
        
        # الهوامش
        margins_label = QLabel("الهوامش (سم):")
        page_settings_layout.addWidget(margins_label)
        
        self.margins_spin = QSpinBox()
        self.margins_spin.setRange(1, 5)
        self.margins_spin.setValue(2)
        page_settings_layout.addWidget(self.margins_spin)
        
        page_layout.addLayout(page_settings_layout)
        layout.addWidget(page_group)
        
        # خيارات إضافية
        options_group = QGroupBox("⚙️ خيارات إضافية")
        options_layout = QVBoxLayout(options_group)
        
        self.include_logo_checkbox = QCheckBox("تضمين شعار العيادة")
        self.include_logo_checkbox.setChecked(True)
        options_layout.addWidget(self.include_logo_checkbox)
        
        self.include_date_checkbox = QCheckBox("تضمين التاريخ والوقت")
        self.include_date_checkbox.setChecked(True)
        options_layout.addWidget(self.include_date_checkbox)
        
        self.include_signature_checkbox = QCheckBox("تضمين مساحة للتوقيع")
        self.include_signature_checkbox.setChecked(True)
        options_layout.addWidget(self.include_signature_checkbox)
        
        layout.addWidget(options_group)
        layout.addStretch()
        
        return widget
    
    def create_preview_tab(self):
        """إنشاء تبويب المعاينة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        preview_label = QLabel("معاينة الوصفة:")
        preview_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(preview_label)
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setMinimumHeight(500)
        layout.addWidget(self.preview_text)
        
        # زر تحديث المعاينة
        update_preview_button = QPushButton("🔄 تحديث المعاينة")
        update_preview_button.clicked.connect(self.update_preview)
        layout.addWidget(update_preview_button)
        
        return widget
    
    def load_settings(self):
        """تحميل الإعدادات من قاعدة البيانات"""
        self.clinic_name_input.setText(self.clinic_settings.get('clinic_name', ''))
        self.doctor_name_input.setText(self.clinic_settings.get('doctor_name', ''))
        self.clinic_address_input.setPlainText(self.clinic_settings.get('clinic_address', ''))
        self.clinic_phone_input.setText(self.clinic_settings.get('clinic_phone', ''))
        
        # تحديث المعاينة
        self.update_preview()
    
    def update_preview(self):
        """تحديث معاينة الوصفة"""
        preview_html = self.generate_prescription_html()
        self.preview_text.setHtml(preview_html)
    
    def generate_prescription_html(self):
        """إنشاء HTML للوصفة"""
        clinic_name = self.clinic_name_input.text() or "عيادة طبية"
        doctor_name = self.doctor_name_input.text() or "د. طبيب"
        clinic_address = self.clinic_address_input.toPlainText() or "عنوان العيادة"
        clinic_phone = self.clinic_phone_input.text() or "رقم الهاتف"
        
        current_date = datetime.now().strftime("%Y/%m/%d")
        current_time = datetime.now().strftime("%H:%M")
        
        treatment_text = self.treatment_text.toPlainText() or "لا يوجد وصف علاج"
        
        html = f"""
        <html dir="rtl">
        <head>
            <meta charset="utf-8">
            <style>
                body {{
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: {self.font_size_spin.value()}px;
                    line-height: 1.6;
                    margin: {self.margins_spin.value()}cm;
                    direction: rtl;
                }}
                .header {{
                    text-align: center;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .clinic-name {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }}
                .doctor-name {{
                    font-size: 18px;
                    color: #34495e;
                    margin-bottom: 5px;
                }}
                .clinic-info {{
                    font-size: 12px;
                    color: #7f8c8d;
                }}
                .patient-info {{
                    background-color: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 20px 0;
                }}
                .patient-title {{
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }}
                .treatment {{
                    margin: 30px 0;
                }}
                .treatment-title {{
                    font-size: 16px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    border-bottom: 1px solid #bdc3c7;
                    padding-bottom: 5px;
                }}
                .treatment-content {{
                    white-space: pre-line;
                    background-color: #ffffff;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    padding: 15px;
                    min-height: 200px;
                }}
                .footer {{
                    margin-top: 50px;
                    text-align: center;
                    font-size: 10px;
                    color: #95a5a6;
                }}
                .signature {{
                    margin-top: 60px;
                    text-align: left;
                }}
                .signature-line {{
                    border-top: 1px solid #bdc3c7;
                    width: 200px;
                    margin: 20px 0 5px auto;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="clinic-name">{clinic_name}</div>
                <div class="doctor-name">{doctor_name}</div>
                <div class="clinic-info">
                    {clinic_address}<br>
                    هاتف: {clinic_phone}
                </div>
            </div>
            
            <div class="patient-info">
                <div class="patient-title">معلومات المريض:</div>
                <strong>الاسم:</strong> {self.patient_data['full_name']}<br>
                <strong>رقم الملف:</strong> {self.patient_data['file_number']}<br>
                <strong>التاريخ:</strong> {current_date} - {current_time}
            </div>
            
            <div class="treatment">
                <div class="treatment-title">💊 وصف العلاج:</div>
                <div class="treatment-content">{treatment_text}</div>
            </div>
            
            {"<div class='signature'><div class='signature-line'></div>توقيع الطبيب</div>" if self.include_signature_checkbox.isChecked() else ""}
            
            <div class="footer">
                تم إنشاء هذه الوصفة بواسطة نظام إدارة العيادة الطبية
            </div>
        </body>
        </html>
        """
        
        return html
    
    def print_prescription(self):
        """طباعة الوصفة"""
        try:
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            printer.setPageSize(QPrinter.PageSize.A4)
            
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec() == QPrintDialog.DialogCode.Accepted:
                document = QTextDocument()
                document.setHtml(self.generate_prescription_html())
                document.print(printer)
                
                QMessageBox.information(self, "نجح", "تم طباعة الوصفة بنجاح")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الطباعة: {str(e)}")
    
    def save_as_pdf(self):
        """حفظ الوصفة كملف PDF"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ الوصفة كـ PDF", 
                f"وصفة_{self.patient_data['full_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF files (*.pdf)"
            )
            
            if file_path:
                printer = QPrinter(QPrinter.PrinterMode.HighResolution)
                printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.PageSize.A4)
                
                document = QTextDocument()
                document.setHtml(self.generate_prescription_html())
                document.print(printer)
                
                QMessageBox.information(self, "نجح", f"تم حفظ الوصفة في:\n{file_path}")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ PDF: {str(e)}")
    
    def show_print_preview(self):
        """عرض معاينة الطباعة"""
        try:
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            printer.setPageSize(QPrinter.PageSize.A4)
            
            preview_dialog = QPrintPreviewDialog(printer, self)
            preview_dialog.paintRequested.connect(self.print_preview)
            preview_dialog.exec()
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عرض المعاينة: {str(e)}")
    
    def print_preview(self, printer):
        """طباعة المعاينة"""
        document = QTextDocument()
        document.setHtml(self.generate_prescription_html())
        document.print(printer)
