from PyQt6.QtWidgets import (Q<PERSON><PERSON>og, QVBox<PERSON><PERSON>out, QHBox<PERSON>ayout, QLabel, 
                            QPushButton, QTextEdit, QFrame, QGroupBox,
                            QLineEdit, QComboBox, QSpinBox, QCheckBox,
                            QFileDialog, QMessageBox, QTabWidget, QWidget)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont, QPixmap, QTextDocument, QTextCursor
try:
    from PyQt6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
    PRINT_SUPPORT_AVAILABLE = True
except ImportError:
    PRINT_SUPPORT_AVAILABLE = False
    print("تحذير: مكتبة الطباعة غير متوفرة")
import os
from datetime import datetime
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

class PrescriptionPrintDialog(QDialog):
    """نافذة طباعة الوصفة الطبية"""
    
    def __init__(self, patient_data, visit_data, clinic_settings, parent=None):
        super().__init__(parent)
        self.patient_data = patient_data
        self.visit_data = visit_data
        self.clinic_settings = clinic_settings
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("طباعة الوصفة الطبية")
        self.setFixedSize(800, 700)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title_label = QLabel("🖨️ طباعة الوصفة الطبية")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب المحتوى
        content_tab = self.create_content_tab()
        tabs.addTab(content_tab, "📄 المحتوى")
        
        # تبويب التنسيق
        format_tab = self.create_format_tab()
        tabs.addTab(format_tab, "🎨 التنسيق")
        
        # تبويب المعاينة
        preview_tab = self.create_preview_tab()
        tabs.addTab(preview_tab, "👁️ المعاينة")
        
        layout.addWidget(tabs)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        print_button = QPushButton("🖨️ طباعة")
        print_button.setFixedHeight(40)
        print_button.setFixedWidth(120)
        print_button.clicked.connect(self.print_prescription)
        buttons_layout.addWidget(print_button)
        
        pdf_button = QPushButton("📄 حفظ PDF")
        pdf_button.setFixedHeight(40)
        pdf_button.setFixedWidth(120)
        pdf_button.clicked.connect(self.save_as_pdf)
        buttons_layout.addWidget(pdf_button)
        
        preview_button = QPushButton("👁️ معاينة الطباعة")
        preview_button.setFixedHeight(40)
        preview_button.setFixedWidth(150)
        preview_button.clicked.connect(self.show_print_preview)
        buttons_layout.addWidget(preview_button)
        
        buttons_layout.addStretch()
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(40)
        cancel_button.setFixedWidth(100)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 1px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
    
    def create_content_tab(self):
        """إنشاء تبويب المحتوى"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # معلومات العيادة
        clinic_group = QGroupBox("🏥 معلومات العيادة")
        clinic_layout = QVBoxLayout(clinic_group)
        
        clinic_info_layout = QHBoxLayout()
        
        # العمود الأيسر
        left_layout = QVBoxLayout()
        
        clinic_name_label = QLabel("اسم العيادة:")
        clinic_name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        left_layout.addWidget(clinic_name_label)
        
        self.clinic_name_input = QLineEdit()
        self.clinic_name_input.setFixedHeight(35)
        left_layout.addWidget(self.clinic_name_input)
        
        doctor_name_label = QLabel("اسم الطبيب:")
        doctor_name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        left_layout.addWidget(doctor_name_label)
        
        self.doctor_name_input = QLineEdit()
        self.doctor_name_input.setFixedHeight(35)
        left_layout.addWidget(self.doctor_name_input)
        
        clinic_info_layout.addLayout(left_layout)
        
        # العمود الأيمن
        right_layout = QVBoxLayout()
        
        clinic_address_label = QLabel("عنوان العيادة:")
        clinic_address_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        right_layout.addWidget(clinic_address_label)
        
        self.clinic_address_input = QTextEdit()
        self.clinic_address_input.setFixedHeight(60)
        right_layout.addWidget(self.clinic_address_input)
        
        clinic_phone_label = QLabel("هاتف العيادة:")
        clinic_phone_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        right_layout.addWidget(clinic_phone_label)
        
        self.clinic_phone_input = QLineEdit()
        self.clinic_phone_input.setFixedHeight(35)
        right_layout.addWidget(self.clinic_phone_input)
        
        clinic_info_layout.addLayout(right_layout)
        clinic_layout.addLayout(clinic_info_layout)
        
        layout.addWidget(clinic_group)
        
        # معلومات المريض
        patient_group = QGroupBox("👤 معلومات المريض")
        patient_layout = QVBoxLayout(patient_group)
        
        patient_info = f"""
اسم المريض: {self.patient_data['full_name']}
رقم الملف: {self.patient_data['file_number']}
الهاتف: {self.patient_data.get('phone', 'غير محدد')}
العمر: {self.patient_data.get('age', 'غير محدد')} سنة
الجنس: {'ذكر' if self.patient_data.get('gender') == 'male' else 'أنثى' if self.patient_data.get('gender') == 'female' else 'غير محدد'}
        """.strip()
        
        patient_info_label = QLabel(patient_info)
        patient_info_label.setStyleSheet("""
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            color: #495057;
        """)
        patient_layout.addWidget(patient_info_label)
        
        layout.addWidget(patient_group)
        
        # وصف العلاج
        treatment_group = QGroupBox("💊 وصف العلاج")
        treatment_layout = QVBoxLayout(treatment_group)
        
        self.treatment_text = QTextEdit()
        self.treatment_text.setMinimumHeight(200)
        self.treatment_text.setPlainText(self.visit_data.get('treatment_description', ''))
        treatment_layout.addWidget(self.treatment_text)
        
        layout.addWidget(treatment_group)
        
        return widget
    
    def create_format_tab(self):
        """إنشاء تبويب التنسيق"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # إعدادات الخط
        font_group = QGroupBox("🔤 إعدادات الخط")
        font_layout = QVBoxLayout(font_group)
        
        font_settings_layout = QHBoxLayout()
        
        # حجم الخط
        font_size_label = QLabel("حجم الخط:")
        font_settings_layout.addWidget(font_size_label)
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(12)
        font_settings_layout.addWidget(self.font_size_spin)
        
        font_settings_layout.addStretch()
        
        font_layout.addLayout(font_settings_layout)
        layout.addWidget(font_group)
        
        # إعدادات الصفحة
        page_group = QGroupBox("📄 إعدادات الصفحة")
        page_layout = QVBoxLayout(page_group)
        
        page_settings_layout = QHBoxLayout()
        
        # حجم الصفحة
        page_size_label = QLabel("حجم الصفحة:")
        page_settings_layout.addWidget(page_size_label)
        
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(["A4", "Letter"])
        page_settings_layout.addWidget(self.page_size_combo)
        
        page_settings_layout.addStretch()
        
        # الهوامش
        margins_label = QLabel("الهوامش (سم):")
        page_settings_layout.addWidget(margins_label)
        
        self.margins_spin = QSpinBox()
        self.margins_spin.setRange(1, 5)
        self.margins_spin.setValue(2)
        page_settings_layout.addWidget(self.margins_spin)
        
        page_layout.addLayout(page_settings_layout)
        layout.addWidget(page_group)
        
        # خيارات إضافية
        options_group = QGroupBox("⚙️ خيارات إضافية")
        options_layout = QVBoxLayout(options_group)
        
        self.include_logo_checkbox = QCheckBox("تضمين شعار العيادة")
        self.include_logo_checkbox.setChecked(True)
        options_layout.addWidget(self.include_logo_checkbox)
        
        self.include_date_checkbox = QCheckBox("تضمين التاريخ والوقت")
        self.include_date_checkbox.setChecked(True)
        options_layout.addWidget(self.include_date_checkbox)
        
        self.include_signature_checkbox = QCheckBox("تضمين مساحة للتوقيع")
        self.include_signature_checkbox.setChecked(True)
        options_layout.addWidget(self.include_signature_checkbox)
        
        layout.addWidget(options_group)
        layout.addStretch()
        
        return widget
    
    def create_preview_tab(self):
        """إنشاء تبويب المعاينة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        preview_label = QLabel("معاينة الوصفة:")
        preview_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(preview_label)
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setMinimumHeight(500)
        layout.addWidget(self.preview_text)
        
        # زر تحديث المعاينة
        update_preview_button = QPushButton("🔄 تحديث المعاينة")
        update_preview_button.clicked.connect(self.update_preview)
        layout.addWidget(update_preview_button)
        
        return widget
    
    def load_settings(self):
        """تحميل الإعدادات من قاعدة البيانات"""
        self.clinic_name_input.setText(self.clinic_settings.get('clinic_name', ''))
        self.doctor_name_input.setText(self.clinic_settings.get('doctor_name', ''))
        self.clinic_address_input.setPlainText(self.clinic_settings.get('clinic_address', ''))
        self.clinic_phone_input.setText(self.clinic_settings.get('clinic_phone', ''))
        
        # تحديث المعاينة
        self.update_preview()
    
    def update_preview(self):
        """تحديث معاينة الوصفة"""
        preview_html = self.generate_prescription_html()
        self.preview_text.setHtml(preview_html)
    
    def generate_prescription_html(self):
        """إنشاء HTML للوصفة"""
        clinic_name = self.clinic_name_input.text() or "عيادة طبية"
        doctor_name = self.doctor_name_input.text() or "د. طبيب"
        clinic_address = self.clinic_address_input.toPlainText() or "عنوان العيادة"
        clinic_phone = self.clinic_phone_input.text() or "رقم الهاتف"
        
        current_date = datetime.now().strftime("%Y/%m/%d")
        current_time = datetime.now().strftime("%H:%M")
        
        treatment_text = self.treatment_text.toPlainText() or "لا يوجد وصف علاج"

        # الحصول على التشخيص إذا كان متوفراً
        diagnosis = ""
        if hasattr(self, 'visit_data') and self.visit_data:
            diagnosis = self.visit_data.get('diagnosis', '')

        # تنسيق الوصفة للطباعة
        formatted_treatment = self.format_treatment_for_print(treatment_text)
        
        html = f"""
        <html dir="rtl">
        <head>
            <meta charset="utf-8">
            <style>
                @page {{
                    size: A4;
                    margin: {max(1.5, self.margins_spin.value())}cm;
                }}
                body {{
                    font-family: 'Arial', 'Tahoma', 'Segoe UI', sans-serif;
                    font-size: {max(16, self.font_size_spin.value() + 4)}px;
                    line-height: 1.8;
                    margin: 0;
                    padding: 20px;
                    direction: rtl;
                    color: #2c3e50;
                    background: #ffffff;
                }}
                .header {{
                    text-align: center;
                    border: 4px solid #3498db;
                    padding: 30px 20px;
                    margin-bottom: 40px;
                    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
                    border-radius: 20px;
                    color: white;
                    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
                }}
                .clinic-name {{
                    font-size: {max(32, self.font_size_spin.value() + 16)}px;
                    font-weight: bold;
                    margin-bottom: 15px;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                    letter-spacing: 1px;
                }}
                .doctor-name {{
                    font-size: {max(24, self.font_size_spin.value() + 8)}px;
                    margin-bottom: 20px;
                    font-weight: 600;
                    opacity: 0.95;
                }}
                .clinic-info {{
                    font-size: {max(18, self.font_size_spin.value() + 2)}px;
                    line-height: 1.6;
                    opacity: 0.9;
                }}
                .patient-info {{
                    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
                    border: 3px solid #27ae60;
                    border-radius: 20px;
                    padding: 30px;
                    margin: 30px 0;
                    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.2);
                    position: relative;
                }}
                .patient-info::before {{
                    content: '';
                    position: absolute;
                    top: -2px;
                    left: -2px;
                    right: -2px;
                    bottom: -2px;
                    background: linear-gradient(45deg, #27ae60, #2ecc71, #27ae60);
                    border-radius: 22px;
                    z-index: -1;
                }}
                .patient-title {{
                    font-weight: bold;
                    color: #27ae60;
                    margin-bottom: 20px;
                    font-size: {max(22, self.font_size_spin.value() + 6)}px;
                    text-align: center;
                    background: white;
                    padding: 15px;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }}
                .patient-details {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    margin-top: 20px;
                }}
                .patient-detail {{
                    background: white;
                    padding: 20px;
                    border-radius: 15px;
                    border-right: 6px solid #27ae60;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    font-size: {max(18, self.font_size_spin.value() + 2)}px;
                    line-height: 1.6;
                }}
                .diagnosis-section {{
                    background: linear-gradient(135deg, #fff8e1 0%, #fce4ec 100%);
                    border: 3px solid #ff9800;
                    border-radius: 20px;
                    padding: 30px;
                    margin: 30px 0;
                    box-shadow: 0 8px 25px rgba(255, 152, 0, 0.2);
                    position: relative;
                }}
                .diagnosis-section::before {{
                    content: '';
                    position: absolute;
                    top: -2px;
                    left: -2px;
                    right: -2px;
                    bottom: -2px;
                    background: linear-gradient(45deg, #ff9800, #ffc107, #ff9800);
                    border-radius: 22px;
                    z-index: -1;
                }}
                .diagnosis-title {{
                    font-weight: bold;
                    color: #e65100;
                    margin-bottom: 20px;
                    font-size: {max(22, self.font_size_spin.value() + 6)}px;
                    text-align: center;
                    background: white;
                    padding: 15px;
                    border-radius: 15px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }}
                .diagnosis-content {{
                    background: white;
                    padding: 25px;
                    border-radius: 15px;
                    border-right: 6px solid #ff9800;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    font-size: {max(18, self.font_size_spin.value() + 2)}px;
                    line-height: 1.8;
                    font-weight: 500;
                }}
                .treatment {{
                    margin: 40px 0;
                }}
                .treatment-title {{
                    font-size: {max(28, self.font_size_spin.value() + 12)}px;
                    font-weight: bold;
                    color: white;
                    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                    padding: 25px;
                    border-radius: 20px;
                    margin-bottom: 30px;
                    text-align: center;
                    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                    letter-spacing: 1px;
                    position: relative;
                }}
                .treatment-title::before {{
                    content: '';
                    position: absolute;
                    top: -3px;
                    left: -3px;
                    right: -3px;
                    bottom: -3px;
                    background: linear-gradient(45deg, #e74c3c, #ec7063, #e74c3c);
                    border-radius: 23px;
                    z-index: -1;
                }}
                .treatment-content {{
                    background: linear-gradient(135deg, #fdf2f2 0%, #fef9e7 100%);
                    border: 4px solid #e74c3c;
                    border-radius: 20px;
                    padding: 35px;
                    min-height: 300px;
                    box-shadow: 0 10px 30px rgba(231, 76, 60, 0.2);
                    position: relative;
                }}
                .treatment-content::before {{
                    content: '';
                    position: absolute;
                    top: -3px;
                    left: -3px;
                    right: -3px;
                    bottom: -3px;
                    background: linear-gradient(45deg, #e74c3c, #ec7063, #e74c3c);
                    border-radius: 23px;
                    z-index: -1;
                }}
                .medication-item {{
                    background: white;
                    margin: 20px 0;
                    padding: 25px;
                    border-radius: 15px;
                    border-right: 8px solid #e74c3c;
                    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
                    font-size: {max(18, self.font_size_spin.value() + 2)}px;
                    line-height: 1.8;
                    font-weight: 500;
                    position: relative;
                    transition: all 0.3s ease;
                }}
                .medication-item::before {{
                    content: '💊';
                    position: absolute;
                    right: -15px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: #e74c3c;
                    color: white;
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 16px;
                    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
                }}
                .footer {{
                    margin-top: 60px;
                    text-align: center;
                    font-size: {max(14, self.font_size_spin.value() - 2)}px;
                    color: #34495e;
                    background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
                    padding: 30px;
                    border-radius: 20px;
                    border: 2px solid #95a5a6;
                    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
                    line-height: 1.8;
                }}
                .signature {{
                    margin-top: 80px;
                    text-align: left;
                    direction: ltr;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    padding: 30px;
                    border-radius: 20px;
                    border: 2px solid #dee2e6;
                    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
                }}
                .signature-line {{
                    border-top: 3px solid #2c3e50;
                    width: 250px;
                    margin: 30px auto 15px auto;
                    position: relative;
                }}
                .signature-line::before {{
                    content: '✍️';
                    position: absolute;
                    right: -20px;
                    top: -15px;
                    font-size: 20px;
                }}
                .signature-text {{
                    text-align: center;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-top: 15px;
                    font-size: {max(18, self.font_size_spin.value() + 2)}px;
                    direction: rtl;
                }}
                .prescription-number {{
                    position: absolute;
                    top: 20px;
                    left: 20px;
                    background: #3498db;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 25px;
                    font-weight: bold;
                    font-size: {max(14, self.font_size_spin.value() - 2)}px;
                    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
                }}
                .validity-notice {{
                    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                    border: 2px solid #ffc107;
                    border-radius: 15px;
                    padding: 20px;
                    margin: 30px 0;
                    text-align: center;
                    font-weight: bold;
                    color: #856404;
                    font-size: {max(16, self.font_size_spin.value())}px;
                    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
                }}
                @media print {{
                    body {{
                        font-size: {max(14, self.font_size_spin.value())}px;
                        -webkit-print-color-adjust: exact;
                        color-adjust: exact;
                    }}
                    .header {{ break-inside: avoid; }}
                    .patient-info {{ break-inside: avoid; }}
                    .diagnosis-section {{ break-inside: avoid; }}
                    .treatment {{ break-inside: avoid; }}
                    .signature {{ break-inside: avoid; }}
                    .footer {{ break-inside: avoid; }}
                    * {{
                        -webkit-print-color-adjust: exact;
                        color-adjust: exact;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="clinic-name">🏥 {clinic_name}</div>
                <div class="doctor-name">👨‍⚕️ {doctor_name}</div>
                <div class="clinic-info">
                    📍 {clinic_address}<br>
                    📞 هاتف: {clinic_phone}
                </div>
            </div>

            <div class="prescription-number">
                وصفة رقم: {datetime.now().strftime('%Y%m%d%H%M%S')}
            </div>

            <div class="patient-info">
                <div class="patient-title">👤 معلومات المريض</div>
                <div class="patient-details">
                    <div class="patient-detail">
                        <strong>👤 الاسم الكامل:</strong><br>
                        {self.patient_data['full_name']}
                    </div>
                    <div class="patient-detail">
                        <strong>📋 رقم الملف:</strong><br>
                        {self.patient_data.get('file_number', 'غير محدد')}
                    </div>
                    <div class="patient-detail">
                        <strong>🎂 العمر:</strong><br>
                        {self.patient_data.get('age', 'غير محدد')} سنة
                    </div>
                    <div class="patient-detail">
                        <strong>⚧ الجنس:</strong><br>
                        {'ذكر' if self.patient_data.get('gender') == 'male' else 'أنثى'}
                    </div>
                </div>
                <div style="text-align: center; margin-top: 20px; padding: 15px; background: white; border-radius: 10px; font-size: {max(16, self.font_size_spin.value())}px;">
                    <strong>📅 تاريخ الزيارة:</strong> {current_date} | <strong>🕐 الوقت:</strong> {current_time}
                </div>
            </div>

            {f'''
            <div class="diagnosis-section">
                <div class="diagnosis-title">🔍 التشخيص</div>
                <div class="diagnosis-content">{diagnosis}</div>
            </div>
            ''' if diagnosis else ''}

            <div class="treatment">
                <div class="treatment-title">💊 الوصفة الطبية</div>
                <div class="treatment-content">{formatted_treatment}</div>
            </div>

            <div class="validity-notice">
                ⚠️ تنبيه هام: هذه الوصفة صالحة لمدة شهر واحد من تاريخ الإصدار
            </div>

            {"<div class='signature'><div class='signature-line'></div><div class='signature-text'>توقيع الطبيب المعالج</div></div>" if self.include_signature_checkbox.isChecked() else ""}

            <div class="footer">
                <div style="margin-bottom: 15px;">
                    🏥 <strong>تم إنشاء هذه الوصفة بواسطة نظام إدارة العيادة الطبية</strong>
                </div>
                <div style="margin-bottom: 10px;">
                    📅 <strong>تاريخ الطباعة:</strong> {datetime.now().strftime('%Y-%m-%d')} |
                    🕐 <strong>وقت الطباعة:</strong> {datetime.now().strftime('%H:%M')}
                </div>
                <div style="font-size: {max(12, self.font_size_spin.value() - 4)}px; color: #7f8c8d;">
                    للاستفسارات والمتابعة، يرجى الاتصال بالعيادة
                </div>
            </div>
        </body>
        </html>
        """
        
        return html

    def format_treatment_for_print(self, treatment):
        """تنسيق الوصفة للطباعة"""
        if not treatment:
            return '<div class="medication-item" style="text-align: center; color: #7f8c8d; font-style: italic;">لا توجد وصفة طبية</div>'

        lines = treatment.split('\n')
        formatted_lines = []
        medication_counter = 1

        for line in lines:
            line = line.strip()
            if line:
                # تنظيف السطر وتحسين التنسيق
                cleaned_line = line

                # إذا كان السطر يبدأ برقم، استخدمه كما هو
                if line[0].isdigit():
                    formatted_lines.append(f'<div class="medication-item">{cleaned_line}</div>')
                # إذا كان يبدأ بـ - أو •، استبدلها برقم
                elif line.startswith('-') or line.startswith('•'):
                    cleaned_line = f"{medication_counter}. {line[1:].strip()}"
                    formatted_lines.append(f'<div class="medication-item">{cleaned_line}</div>')
                    medication_counter += 1
                else:
                    # إذا لم يكن مرقم، أضف ترقيم
                    if not any(char.isdigit() for char in line[:3]):
                        cleaned_line = f"{medication_counter}. {line}"
                        medication_counter += 1
                    formatted_lines.append(f'<div class="medication-item">{cleaned_line}</div>')

        if not formatted_lines:
            return '<div class="medication-item" style="text-align: center; color: #7f8c8d; font-style: italic;">لا توجد وصفة طبية</div>'

        # إضافة عداد الأدوية
        total_medications = len(formatted_lines)
        header = f'<div style="text-align: center; background: #e74c3c; color: white; padding: 15px; border-radius: 10px; margin-bottom: 20px; font-weight: bold;">إجمالي الأدوية: {total_medications} دواء</div>'

        return header + '\n'.join(formatted_lines)
    
    def print_prescription(self):
        """طباعة الوصفة"""
        if not PRINT_SUPPORT_AVAILABLE:
            QMessageBox.warning(self, "خطأ", "مكتبة الطباعة غير متوفرة")
            return

        try:
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            self.setup_printer_page_size(printer)
            
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec() == QPrintDialog.DialogCode.Accepted:
                document = QTextDocument()
                document.setHtml(self.generate_prescription_html())
                document.print(printer)
                
                QMessageBox.information(self, "نجح", "تم طباعة الوصفة بنجاح")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الطباعة: {str(e)}")
    
    def save_as_pdf(self):
        """حفظ الوصفة كملف PDF"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ الوصفة كـ PDF", 
                f"وصفة_{self.patient_data['full_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF files (*.pdf)"
            )
            
            if file_path:
                printer = QPrinter(QPrinter.PrinterMode.HighResolution)
                printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)
                printer.setOutputFileName(file_path)
                self.setup_printer_page_size(printer)
                
                document = QTextDocument()
                document.setHtml(self.generate_prescription_html())
                document.print(printer)
                
                QMessageBox.information(self, "نجح", f"تم حفظ الوصفة في:\n{file_path}")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ PDF: {str(e)}")
    
    def show_print_preview(self):
        """عرض معاينة الطباعة"""
        if not PRINT_SUPPORT_AVAILABLE:
            QMessageBox.warning(self, "خطأ", "مكتبة الطباعة غير متوفرة")
            return

        try:
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            self.setup_printer_page_size(printer)
            
            preview_dialog = QPrintPreviewDialog(printer, self)
            preview_dialog.paintRequested.connect(self.print_preview)
            preview_dialog.exec()
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عرض المعاينة: {str(e)}")

    def setup_printer_page_size(self, printer):
        """إعداد حجم الصفحة للطابعة"""
        try:
            # محاولة استخدام QPageSize الجديد
            from PyQt6.QtGui import QPageSize
            page_size = QPageSize(QPageSize.PageSizeId.A4)
            printer.setPageSize(page_size)
        except ImportError:
            try:
                # محاولة استخدام الطريقة القديمة
                printer.setPageSize(QPrinter.PageSize.A4)
            except AttributeError:
                try:
                    # محاولة أخرى مع enum
                    from PyQt6.QtPrintSupport import QPrinter
                    printer.setPageSize(QPrinter.A4)
                except:
                    # إعداد افتراضي
                    print("تحذير: لا يمكن تعيين حجم الصفحة")
        except Exception as e:
            print(f"خطأ في إعداد حجم الصفحة: {e}")
    
    def print_preview(self, printer):
        """طباعة المعاينة"""
        document = QTextDocument()
        document.setHtml(self.generate_prescription_html())
        document.print(printer)
