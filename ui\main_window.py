import sys
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QStackedWidget, QPushButton, QLabel, QFrame,
                            QMessageBox, QApplication, QMenuBar, QStatusBar)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import <PERSON><PERSON>ont, QIcon, QAction
from datetime import datetime

from database.database import DatabaseManager
from models.patient import Patient
from models.visit import Visit
from models.medication import Medication
from models.user import User
from models.diagnosis import Diagnosis
from ui.dashboard_widget import DashboardWidget
from ui.patients_widget import PatientsWidget
from ui.visits_widget import VisitsWidget
from ui.today_visits_widget import TodayVisitsWidget
from ui.medications_widget import MedicationsWidget
from ui.settings_widget import SettingsWidget
from ui.users_management_widget import UsersManagementWidget
from ui.reports_widget import ReportsWidget

class MainWindow(QMainWindow):
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.db_manager = DatabaseManager()
        self.patient_model = Patient(self.db_manager)
        self.visit_model = Visit(self.db_manager)
        self.medication_model = Medication(self.db_manager)
        self.user_model = User(self.db_manager)
        
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"إدارة العيادة الطبية - {self.user_data['full_name']}")
        self.setGeometry(100, 100, 1200, 800)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QFrame#sidebarFrame {
                background-color: #2c3e50;
                border-right: 3px solid #34495e;
            }
            QPushButton#sidebarButton {
                background-color: transparent;
                color: white;
                border: none;
                padding: 15px 20px;
                text-align: left;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton#sidebarButton:hover {
                background-color: #34495e;
            }
            QPushButton#sidebarButton:pressed {
                background-color: #1abc9c;
            }
            QPushButton#activeButton {
                background-color: #1abc9c;
                color: white;
            }
            QLabel#headerLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 20px;
                background-color: #2c3e50;
            }
            QLabel#userLabel {
                color: #bdc3c7;
                font-size: 12px;
                padding: 10px 20px;
                background-color: #2c3e50;
            }
            QFrame#contentFrame {
                background-color: white;
                border-radius: 5px;
                margin: 10px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي
        self.create_sidebar(main_layout)
        
        # منطقة المحتوى
        self.create_content_area(main_layout)
        
        # شريط القوائم
        self.create_menu_bar()

        # شريط الحالة
        self.create_status_bar()

        # إعداد المؤقت
        self.setup_timer()

        # عرض الصفحة الرئيسية
        self.show_dashboard()
    
    def create_sidebar(self, main_layout):
        """إنشاء الشريط الجانبي"""
        sidebar_frame = QFrame()
        sidebar_frame.setObjectName("sidebarFrame")
        sidebar_frame.setFixedWidth(250)
        
        sidebar_layout = QVBoxLayout(sidebar_frame)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # عنوان التطبيق
        header_label = QLabel("إدارة العيادة الطبية")
        header_label.setObjectName("headerLabel")
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        sidebar_layout.addWidget(header_label)
        
        # معلومات المستخدم
        role_text = "طبيب" if self.user_data['role'] == 'doctor' else "سكرتير"
        user_label = QLabel(f"{self.user_data['full_name']}\n{role_text}")
        user_label.setObjectName("userLabel")
        sidebar_layout.addWidget(user_label)
        
        # أزرار التنقل
        self.nav_buttons = {}
        
        # الصفحة الرئيسية
        dashboard_btn = QPushButton("🏠 الصفحة الرئيسية")
        dashboard_btn.setObjectName("sidebarButton")
        dashboard_btn.clicked.connect(self.show_dashboard)
        self.nav_buttons['dashboard'] = dashboard_btn
        sidebar_layout.addWidget(dashboard_btn)
        
        # المرضى
        patients_btn = QPushButton("👥 إدارة المرضى")
        patients_btn.setObjectName("sidebarButton")
        patients_btn.clicked.connect(self.show_patients)
        self.nav_buttons['patients'] = patients_btn
        sidebar_layout.addWidget(patients_btn)
        
        # زيارات اليوم
        today_visits_btn = QPushButton("📅 زيارات اليوم")
        today_visits_btn.setObjectName("sidebarButton")
        today_visits_btn.clicked.connect(self.show_today_visits)
        self.nav_buttons['today_visits'] = today_visits_btn
        sidebar_layout.addWidget(today_visits_btn)
        
        # الأدوية (للطبيب فقط)
        if self.user_data['role'] == 'doctor':
            medications_btn = QPushButton("💊 إدارة الأدوية")
            medications_btn.setObjectName("sidebarButton")
            medications_btn.clicked.connect(self.show_medications)
            self.nav_buttons['medications'] = medications_btn
            sidebar_layout.addWidget(medications_btn)

        # إدارة المستخدمين (للمدير فقط)
        if self.user_data['role'] == 'admin':
            users_btn = QPushButton("👥 إدارة المستخدمين")
            users_btn.setObjectName("sidebarButton")
            users_btn.clicked.connect(self.show_users_management)
            self.nav_buttons['users'] = users_btn
            sidebar_layout.addWidget(users_btn)
        
        # التقارير
        reports_btn = QPushButton("📊 التقارير")
        reports_btn.setObjectName("sidebarButton")
        reports_btn.clicked.connect(self.show_reports)
        self.nav_buttons['reports'] = reports_btn
        sidebar_layout.addWidget(reports_btn)
        
        # الإعدادات
        settings_btn = QPushButton("⚙️ الإعدادات")
        settings_btn.setObjectName("sidebarButton")
        settings_btn.clicked.connect(self.show_settings)
        self.nav_buttons['settings'] = settings_btn
        sidebar_layout.addWidget(settings_btn)
        
        # مساحة فارغة
        sidebar_layout.addStretch()
        
        # زر تسجيل الخروج
        logout_btn = QPushButton("🚪 تسجيل الخروج")
        logout_btn.setObjectName("sidebarButton")
        logout_btn.clicked.connect(self.logout)
        sidebar_layout.addWidget(logout_btn)
        
        main_layout.addWidget(sidebar_frame)
    
    def create_content_area(self, main_layout):
        """إنشاء منطقة المحتوى"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        
        content_layout = QVBoxLayout(content_frame)
        
        # StackedWidget للصفحات المختلفة
        self.stacked_widget = QStackedWidget()
        content_layout.addWidget(self.stacked_widget)

        # إنشاء الصفحات
        self.create_pages()

        # عرض لوحة التحكم كصفحة افتراضية
        self.show_dashboard()
        
        main_layout.addWidget(content_frame)

    def create_pages(self):
        """إنشاء صفحات التطبيق"""
        # صفحة لوحة التحكم (الصفحة الرئيسية)
        self.dashboard_widget = DashboardWidget(self.db_manager, self.user_data)
        self.stacked_widget.addWidget(self.dashboard_widget)  # Index 0

        # صفحة المرضى
        self.patients_widget = PatientsWidget(self.patient_model)
        self.patients_widget.patient_selected.connect(self.on_patient_selected)
        self.stacked_widget.addWidget(self.patients_widget)  # Index 1

        # صفحة زيارات اليوم
        self.today_visits_widget = TodayVisitsWidget(self.visit_model, self.patient_model)
        self.stacked_widget.addWidget(self.today_visits_widget)  # Index 2

        # صفحة الأدوية (للطبيب فقط)
        if self.user_data['role'] == 'doctor':
            self.medications_widget = MedicationsWidget(self.medication_model)
            self.stacked_widget.addWidget(self.medications_widget)  # Index 3
            self.medications_index = 3
        else:
            self.medications_index = None

        # صفحة إدارة المستخدمين (للمدير فقط)
        if self.user_data['role'] == 'admin':
            self.users_management_widget = UsersManagementWidget(self.user_model, self.user_data)
            self.stacked_widget.addWidget(self.users_management_widget)  # Index 4 (أو 3 للطبيب)
            self.users_index = 4 if self.user_data['role'] == 'doctor' else 3
        else:
            self.users_index = None

        # صفحة التقارير
        self.reports_widget = ReportsWidget(self.db_manager)
        self.stacked_widget.addWidget(self.reports_widget)
        # تحديد الفهرس بناءً على الدور
        if self.user_data['role'] == 'admin':
            self.reports_index = 5 if self.user_data['role'] == 'doctor' else 4
        else:
            self.reports_index = 4 if self.user_data['role'] == 'doctor' else 3

        # صفحة الإعدادات
        self.settings_widget = SettingsWidget(self.db_manager)
        self.stacked_widget.addWidget(self.settings_widget)
        # تحديد الفهرس بناءً على الدور
        if self.user_data['role'] == 'admin':
            self.settings_index = 6 if self.user_data['role'] == 'doctor' else 5
        else:
            self.settings_index = 5 if self.user_data['role'] == 'doctor' else 4

        # صفحة زيارات المريض (ستظهر عند اختيار مريض)
        self.visits_widget = None  # سيتم إنشاؤها عند الحاجة

    def on_patient_selected(self, patient_data):
        """التعامل مع اختيار مريض - عرض زيارات المريض"""
        try:
            # إنشاء أو تحديث صفحة زيارات المريض
            if self.visits_widget:
                self.stacked_widget.removeWidget(self.visits_widget)
                self.visits_widget.deleteLater()

            self.visits_widget = VisitsWidget(self.visit_model, patient_data)
            self.stacked_widget.addWidget(self.visits_widget)

            # الانتقال إلى صفحة زيارات المريض
            self.stacked_widget.setCurrentWidget(self.visits_widget)
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage(f"عرض زيارات المريض: {patient_data['full_name']}")

            # تحديث الزر النشط (إلغاء تحديد جميع الأزرار)
            for button in self.nav_buttons.values():
                button.setObjectName("sidebarButton")
                button.style().unpolish(button)
                button.style().polish(button)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عرض زيارات المريض: {str(e)}")
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu('ملف')
        
        backup_action = QAction('إنشاء نسخة احتياطية', self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة عرض
        view_menu = menubar.addMenu('عرض')
        
        refresh_action = QAction('تحديث', self)
        refresh_action.triggered.connect(self.refresh_current_view)
        view_menu.addAction(refresh_action)
        
        # قائمة مساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("مرحباً بك في نظام إدارة العيادة الطبية")
        
        # عرض التاريخ والوقت
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        self.update_time()
    
    def setup_timer(self):
        """إعداد مؤقت تحديث الوقت"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # تحديث كل ثانية
    
    def update_time(self):
        """تحديث عرض الوقت"""
        current_time = datetime.now().strftime("%Y/%m/%d - %H:%M:%S")
        self.time_label.setText(current_time)
    
    def set_active_button(self, button_name):
        """تحديد الزر النشط"""
        for name, button in self.nav_buttons.items():
            if name == button_name:
                button.setObjectName("activeButton")
            else:
                button.setObjectName("sidebarButton")
            button.style().unpolish(button)
            button.style().polish(button)
    
    def show_dashboard(self):
        """عرض الصفحة الرئيسية"""
        self.set_active_button('dashboard')
        self.stacked_widget.setCurrentIndex(0)  # صفحة لوحة التحكم
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage("الصفحة الرئيسية")

    def show_patients(self):
        """عرض صفحة المرضى"""
        self.set_active_button('patients')
        self.stacked_widget.setCurrentIndex(1)  # صفحة المرضى
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage("إدارة المرضى")
    
    def show_today_visits(self):
        """عرض زيارات اليوم"""
        self.set_active_button('today_visits')
        self.stacked_widget.setCurrentIndex(2)  # صفحة زيارات اليوم
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage("زيارات اليوم")
    
    def show_medications(self):
        """عرض صفحة الأدوية"""
        if self.user_data['role'] == 'doctor' and self.medications_index is not None:
            self.set_active_button('medications')
            self.stacked_widget.setCurrentIndex(self.medications_index)
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage("إدارة الأدوية")
        else:
            QMessageBox.warning(self, "غير مسموح", "هذه الصفحة متاحة للطبيب فقط")
    
    def show_reports(self):
        """عرض صفحة التقارير"""
        self.set_active_button('reports')
        self.stacked_widget.setCurrentIndex(self.reports_index)
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage("التقارير والإحصائيات")
    
    def show_users_management(self):
        """عرض صفحة إدارة المستخدمين"""
        if self.user_data['role'] == 'admin' and self.users_index is not None:
            self.set_active_button('users')
            self.stacked_widget.setCurrentIndex(self.users_index)
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage("إدارة المستخدمين")
        else:
            QMessageBox.warning(self, "غير مسموح", "هذه الصفحة متاحة للمدير فقط")

    def show_settings(self):
        """عرض صفحة الإعدادات"""
        self.set_active_button('settings')
        self.stacked_widget.setCurrentIndex(self.settings_index)
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage("الإعدادات")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            backup_path = self.db_manager.backup_database()
            QMessageBox.information(self, "نجح الحفظ", 
                                  f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}")
    
    def refresh_current_view(self):
        """تحديث العرض الحالي"""
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage("تم التحديث", 2000)
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(self, "حول البرنامج", 
                         "نظام إدارة العيادة الطبية\nالإصدار 1.0\n\nتم تطويره باستخدام Python و PyQt6")
    
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(self, "تسجيل الخروج", 
                                   "هل أنت متأكد من تسجيل الخروج؟",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            self.close()
    
    def closeEvent(self, event):
        """التعامل مع إغلاق النافذة"""
        reply = QMessageBox.question(self, "إغلاق البرنامج", 
                                   "هل أنت متأكد من إغلاق البرنامج؟",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            # إنشاء نسخة احتياطية تلقائية
            auto_backup = self.db_manager.get_setting('auto_backup')
            if auto_backup == 'true':
                try:
                    self.db_manager.backup_database()
                except:
                    pass  # تجاهل أخطاء النسخ الاحتياطي عند الإغلاق
            
            event.accept()
        else:
            event.ignore()
