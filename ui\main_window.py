import sys
from PyQt6.QtWidgets import (QMain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QStackedWidget, QPushButton, QLabel, QFrame,
                            QMessageBox, QApplication, QMenuBar, QStatusBar)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import <PERSON><PERSON>ont, QIcon, QAction
from datetime import datetime

from database.database import DatabaseManager
from styles.style_manager import StyleManager
from models.patient import Patient
from models.visit import Visit
from models.medication import Medication
from models.user import User
from models.diagnosis import Diagnosis
from ui.dashboard_widget import DashboardWidget
from ui.patients_widget import PatientsWidget
from ui.visits_widget import VisitsWidget
from ui.today_visits_widget import TodayVisitsWidget
from ui.medications_widget import MedicationsWidget
from ui.settings_widget import SettingsWidget
from ui.users_management_widget import UsersManagementWidget
from ui.reports_widget import ReportsWidget

class MainWindow(QMainWindow):
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.db_manager = DatabaseManager()
        self.style_manager = StyleManager()
        self.patient_model = Patient(self.db_manager)
        self.visit_model = Visit(self.db_manager)
        self.medication_model = Medication(self.db_manager)
        self.user_model = User(self.db_manager)
        
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """إعداد واجهة المستخدم العصرية"""
        self.setWindowTitle(f"إدارة العيادة الطبية - {self.user_data['full_name']}")
        self.setGeometry(100, 100, 1400, 900)
        
        # تطبيق الأنماط العصرية الجديدة
        combined_style = (
            self.style_manager.get_main_style() +
            self.style_manager.get_button_style() +
            self.style_manager.get_input_style() +
            self.style_manager.get_table_style() +
            self.style_manager.get_modern_sidebar_style() +
            self.style_manager.get_modern_card_style()
        )
        self.setStyleSheet(combined_style)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي
        self.create_sidebar(main_layout)
        
        # منطقة المحتوى
        self.create_content_area(main_layout)
        
        # شريط القوائم
        self.create_menu_bar()

        # شريط الحالة
        self.create_status_bar()

        # عرض الصفحة الرئيسية
        self.show_dashboard()
    
    def create_sidebar(self, main_layout):
        """إنشاء الشريط الجانبي العصري"""
        sidebar_frame = QFrame()
        sidebar_frame.setObjectName("sidebarFrame")
        sidebar_frame.setMinimumWidth(250)
        sidebar_frame.setMaximumWidth(320)
        
        sidebar_layout = QVBoxLayout(sidebar_frame)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(5)
        
        # عنوان التطبيق
        header_label = QLabel("إدارة العيادة الطبية")
        header_label.setObjectName("headerLabel")
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        sidebar_layout.addWidget(header_label)
        
        # معلومات المستخدم
        role_text = "طبيب" if self.user_data['role'] == 'doctor' else "سكرتير" if self.user_data['role'] == 'secretary' else "مدير"
        user_label = QLabel(f"{self.user_data['full_name']}\n{role_text}")
        user_label.setObjectName("userLabel")
        user_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        sidebar_layout.addWidget(user_label)
        
        # أزرار التنقل العصرية
        self.nav_buttons = {}
        
        # الصفحة الرئيسية
        dashboard_btn = QPushButton("🏠  الصفحة الرئيسية")
        dashboard_btn.setObjectName("sidebarButton")
        dashboard_btn.clicked.connect(self.show_dashboard)
        dashboard_btn.setCheckable(True)
        dashboard_btn.setChecked(True)
        self.nav_buttons['dashboard'] = dashboard_btn
        sidebar_layout.addWidget(dashboard_btn)
        
        # المرضى
        patients_btn = QPushButton("👥  إدارة المرضى")
        patients_btn.setObjectName("sidebarButton")
        patients_btn.clicked.connect(self.show_patients)
        patients_btn.setCheckable(True)
        self.nav_buttons['patients'] = patients_btn
        sidebar_layout.addWidget(patients_btn)
        
        # زيارات اليوم
        today_visits_btn = QPushButton("📅  زيارات اليوم")
        today_visits_btn.setObjectName("sidebarButton")
        today_visits_btn.clicked.connect(self.show_today_visits)
        today_visits_btn.setCheckable(True)
        self.nav_buttons['today_visits'] = today_visits_btn
        sidebar_layout.addWidget(today_visits_btn)
        
        # الأدوية (للطبيب فقط)
        if self.user_data['role'] == 'doctor':
            medications_btn = QPushButton("💊  إدارة الأدوية")
            medications_btn.setObjectName("sidebarButton")
            medications_btn.clicked.connect(self.show_medications)
            medications_btn.setCheckable(True)
            self.nav_buttons['medications'] = medications_btn
            sidebar_layout.addWidget(medications_btn)

        # إدارة المستخدمين (للمدير فقط)
        if self.user_data['role'] == 'admin':
            users_btn = QPushButton("👤  إدارة المستخدمين")
            users_btn.setObjectName("sidebarButton")
            users_btn.clicked.connect(self.show_users_management)
            users_btn.setCheckable(True)
            self.nav_buttons['users'] = users_btn
            sidebar_layout.addWidget(users_btn)
        
        # التقارير
        reports_btn = QPushButton("📊  التقارير")
        reports_btn.setObjectName("sidebarButton")
        reports_btn.clicked.connect(self.show_reports)
        reports_btn.setCheckable(True)
        self.nav_buttons['reports'] = reports_btn
        sidebar_layout.addWidget(reports_btn)
        
        # الإعدادات
        settings_btn = QPushButton("⚙️  الإعدادات")
        settings_btn.setObjectName("sidebarButton")
        settings_btn.clicked.connect(self.show_settings)
        settings_btn.setCheckable(True)
        self.nav_buttons['settings'] = settings_btn
        sidebar_layout.addWidget(settings_btn)
        
        # مساحة فارغة
        sidebar_layout.addStretch()
        
        # زر تسجيل الخروج
        logout_btn = QPushButton("🚪  تسجيل الخروج")
        logout_btn.setObjectName("sidebarButton")
        logout_btn.clicked.connect(self.logout)
        sidebar_layout.addWidget(logout_btn)
        
        main_layout.addWidget(sidebar_frame)
    
    def create_content_area(self, main_layout):
        """إنشاء منطقة المحتوى"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        
        content_layout = QVBoxLayout(content_frame)
        
        # StackedWidget للصفحات المختلفة
        self.stacked_widget = QStackedWidget()
        content_layout.addWidget(self.stacked_widget)

        # إنشاء الصفحات
        self.create_pages()

        # عرض لوحة التحكم كصفحة افتراضية
        self.show_dashboard()
        
        main_layout.addWidget(content_frame)

    def create_pages(self):
        """إنشاء صفحات التطبيق"""
        # صفحة لوحة التحكم (الصفحة الرئيسية)
        self.dashboard_widget = DashboardWidget(self.db_manager, self.user_data)
        self.stacked_widget.addWidget(self.dashboard_widget)  # Index 0

        # صفحة المرضى
        self.patients_widget = PatientsWidget(self.patient_model)
        self.patients_widget.patient_selected.connect(self.on_patient_selected)
        self.stacked_widget.addWidget(self.patients_widget)  # Index 1

        # صفحة زيارات اليوم
        self.today_visits_widget = TodayVisitsWidget(self.visit_model, self.patient_model)
        self.stacked_widget.addWidget(self.today_visits_widget)  # Index 2

        # صفحة الأدوية (للطبيب فقط)
        if self.user_data['role'] == 'doctor':
            self.medications_widget = MedicationsWidget(self.medication_model)
            self.stacked_widget.addWidget(self.medications_widget)  # Index 3
            self.medications_index = 3
        else:
            self.medications_index = None

        # صفحة إدارة المستخدمين (للمدير فقط)
        if self.user_data['role'] == 'admin':
            self.users_management_widget = UsersManagementWidget(self.user_model, self.user_data)
            self.stacked_widget.addWidget(self.users_management_widget)  # Index 4 (أو 3 للطبيب)
            self.users_index = 4 if self.user_data['role'] == 'doctor' else 3
        else:
            self.users_index = None

        # صفحة التقارير
        self.reports_widget = ReportsWidget(self.db_manager)
        self.stacked_widget.addWidget(self.reports_widget)
        # تحديد الفهرس بناءً على الدور
        if self.user_data['role'] == 'admin':
            self.reports_index = 5 if self.user_data['role'] == 'doctor' else 4
        else:
            self.reports_index = 4 if self.user_data['role'] == 'doctor' else 3

        # صفحة الإعدادات
        self.settings_widget = SettingsWidget(self.db_manager)
        self.stacked_widget.addWidget(self.settings_widget)
        # تحديد الفهرس بناءً على الدور
        if self.user_data['role'] == 'admin':
            self.settings_index = 6 if self.user_data['role'] == 'doctor' else 5
        else:
            self.settings_index = 5 if self.user_data['role'] == 'doctor' else 4

        # صفحة زيارات المريض (ستظهر عند اختيار مريض)
        self.visits_widget = None  # سيتم إنشاؤها عند الحاجة

    def on_patient_selected(self, patient_data):
        """التعامل مع اختيار مريض - عرض زيارات المريض"""
        try:
            # إنشاء أو تحديث صفحة زيارات المريض
            if self.visits_widget:
                self.stacked_widget.removeWidget(self.visits_widget)
                self.visits_widget.deleteLater()

            self.visits_widget = VisitsWidget(self.visit_model, patient_data)
            self.stacked_widget.addWidget(self.visits_widget)

            # الانتقال إلى صفحة زيارات المريض
            index = self.stacked_widget.indexOf(self.visits_widget)
            self.stacked_widget.setCurrentIndex(index)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في عرض زيارات المريض: {str(e)}")

    def update_active_button(self, active_button_key):
        """تحديث حالة الزر النشط في الشريط الجانبي"""
        for key, button in self.nav_buttons.items():
            if key == active_button_key:
                button.setChecked(True)
            else:
                button.setChecked(False)
    
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.stacked_widget.setCurrentIndex(0)
        self.update_active_button('dashboard')
        
    def show_patients(self):
        """عرض صفحة المرضى"""
        self.stacked_widget.setCurrentIndex(1)
        self.update_active_button('patients')
        
    def show_today_visits(self):
        """عرض زيارات اليوم"""
        self.stacked_widget.setCurrentIndex(2)
        self.update_active_button('today_visits')
        
    def show_medications(self):
        """عرض صفحة الأدوية"""
        if self.medications_index is not None:
            self.stacked_widget.setCurrentIndex(self.medications_index)
            self.update_active_button('medications')
            
    def show_users_management(self):
        """عرض صفحة إدارة المستخدمين"""
        if self.users_index is not None:
            self.stacked_widget.setCurrentIndex(self.users_index)
            self.update_active_button('users')
            
    def show_reports(self):
        """عرض صفحة التقارير"""
        self.stacked_widget.setCurrentIndex(self.reports_index)
        self.update_active_button('reports')
        
    def show_settings(self):
        """عرض صفحة الإعدادات"""
        self.stacked_widget.setCurrentIndex(self.settings_index)
        self.update_active_button('settings')

    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        # خيار إنشاء نسخة احتياطية
        backup_action = QAction('إنشاء نسخة احتياطية', self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        # فاصل
        file_menu.addSeparator()
        
        # خيار الخروج
        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        # حول التطبيق
        about_action = QAction('حول التطبيق', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        
        # عرض الوقت الحالي
        self.time_label = QLabel()
        status_bar.addPermanentWidget(self.time_label)
        
        # عرض معلومات المستخدم
        user_info = f"المستخدم: {self.user_data['username']} | الدور: {self.user_data['role']}"
        status_bar.showMessage(user_info)

    def setup_timer(self):
        """إعداد المؤقت لتحديث الوقت"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # تحديث كل ثانية
        self.update_time()  # تحديث فوري

    def update_time(self):
        """تحديث عرض الوقت"""
        if hasattr(self, 'time_label'):
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.time_label.setText(current_time)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            backup_path = self.db_manager.backup_database()
            QMessageBox.information(
                self,
                "نجح إنشاء النسخة الاحتياطية",
                f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_path}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في النسخة الاحتياطية",
                f"فشل في إنشاء النسخة الاحتياطية: {str(e)}"
            )

    def show_about(self):
        """عرض معلومات التطبيق"""
        QMessageBox.about(
            self,
            "حول التطبيق",
            """
            نظام إدارة العيادة الطبية
            الإصدار: 2.0
            
            نظام شامل لإدارة العيادات الطبية
            يشمل إدارة المرضى والزيارات والأدوية والتقارير
            
            تم تطويره باستخدام Python و PyQt6
            """
        )

    def closeEvent(self, event):
        """التعامل مع إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            'تأكيد الإغلاق',
            'هل تريد إغلاق التطبيق؟',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # عمل نسخة احتياطية عند الإغلاق
            if hasattr(self, 'db_manager'):
                try:
                    self.db_manager.backup_database()
                except:
                    pass  # تجاهل أخطاء النسخ الاحتياطي عند الإغلاق
            
            event.accept()
        else:
            event.ignore()
            
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self,
            'تأكيد تسجيل الخروج',
            'هل تريد تسجيل الخروج؟',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.close()
            # يمكن إضافة كود إضافي هنا لإعادة فتح نافذة تسجيل الدخول
