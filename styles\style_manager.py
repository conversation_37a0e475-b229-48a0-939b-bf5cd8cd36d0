"""
مدير الأنماط المركزي للنظام
Global Style Manager for Medical Clinic System
"""

import os
from PyQt6.QtGui import QFont, QFontDatabase
from PyQt6.QtCore import QDir

class StyleManager:
    """مدير الأنماط المركزي"""
    
    def __init__(self):
        self.base_font_size = 14
        self.setup_fonts()
        # ألوان التصميم العصري المطابق للصورة المرفقة
        self.primary_blue = "#007ACC"      # الأزرق الرئيسي للأزرار النشطة
        self.dark_blue = "#005A9B"        # أزرق داكن للتحويم
        self.light_blue = "#E6F3FF"       # أزرق فاتح للخلفيات
        self.sidebar_blue = "#1E3A8A"     # أزرق الشريط الجانبي
        self.accent_blue = "#3B82F6"      # أزرق مميز
        self.white = "#FFFFFF"
        self.gray_light = "#F8FAFC"       # رمادي فاتح جداً
        self.gray_medium = "#E2E8F0"      # رمادي متوسط
        self.gray_dark = "#64748B"        # رمادي داكن
        self.text_dark = "#1E293B"        # لون النص الداكن
        self.success_green = "#10B981"
        self.warning_orange = "#F59E0B"
        self.danger_red = "#EF4444"
    
    def setup_fonts(self):
        """إعداد الخطوط المطلوبة"""
        # لا نحتاج إعداد خطوط خاصة حالياً
        pass
    
    def get_main_style(self):
        """النمط الرئيسي للتطبيق - واجهة عصرية"""
        return f"""
            QMainWindow {{
                background-color: {self.gray_light};
            }}
            
            QWidget {{
                font-family: 'Segoe UI', 'Roboto', 'Inter', sans-serif;
                font-size: {self.base_font_size}px;
                font-weight: 400;
                color: {self.text_dark};
                background-color: {self.white};
            }}
            
            /* العناوين الرئيسية */
            QLabel#mainTitle {{
                font-size: {self.base_font_size + 8}px;
                font-weight: 700;
                color: {self.text_dark};
                padding: 15px 0;
                margin-bottom: 10px;
            }}
            
            QLabel#sectionTitle {{
                font-size: {self.base_font_size + 4}px;
                font-weight: 600;
                color: {self.text_dark};
                padding: 10px 0;
            }}
            
            QLabel#headerLabel {{
                font-size: {self.base_font_size + 2}px;
                font-weight: 700;
                color: {self.white};
                padding: 20px;
                background-color: transparent;
            }}
            
            QLabel#userLabel {{
                font-size: {self.base_font_size - 1}px;
                font-weight: 500;
                color: {self.white};
                padding: 10px 20px;
                margin-bottom: 20px;
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                margin-left: 10px;
                margin-right: 10px;
            }}
            
            /* النصوص العادية */
            QLabel {{
                font-size: {self.base_font_size}px;
                font-weight: 400;
                color: {self.text_dark};
                line-height: 1.4;
            }}
            
            QLabel#smallText {{
                font-size: {self.base_font_size - 2}px;
                font-weight: 400;
                color: {self.gray_dark};
            }}
        """
    
    def get_button_style(self):
        """أنماط الأزرار العصرية"""
        return f"""
            /* الأزرار العادية */
            QPushButton {{
                font-size: {self.base_font_size}px;
                font-weight: 600;
                color: {self.white};
                background-color: {self.primary_blue};
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                min-width: 120px;
                min-height: 36px;
                text-align: center;
            }}
            
            QPushButton:hover {{
                background-color: {self.dark_blue};
                transform: scale(1.02);
            }}
            
            QPushButton:pressed {{
                background-color: {self.dark_blue};
                transform: scale(0.98);
            }}
            
            QPushButton:disabled {{
                background-color: {self.gray_medium};
                color: {self.gray_dark};
            }}
            
            /* أزرار الشريط الجانبي */
            QPushButton#sidebarButton {{
                font-size: {self.base_font_size}px;
                font-weight: 500;
                color: {self.white};
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 15px 20px;
                margin: 2px 10px;
                text-align: left;
                min-height: 45px;
            }}
            
            QPushButton#sidebarButton:hover {{
                background-color: rgba(255, 255, 255, 0.1);
                border-left: 4px solid {self.white};
            }}
            
            QPushButton#sidebarButton:pressed {{
                background-color: rgba(255, 255, 255, 0.2);
            }}
            
            QPushButton#sidebarButton:checked {{
                background-color: rgba(255, 255, 255, 0.15);
                border-left: 4px solid {self.white};
                font-weight: 600;
            }}
            
            /* أزرار النجاح */
            QPushButton#successButton {{
                background-color: {self.success_green};
            }}
            
            QPushButton#successButton:hover {{
                background-color: #059669;
            }}
            
            /* أزرار التحذير */
            QPushButton#warningButton {{
                background-color: {self.warning_orange};
            }}
            
            QPushButton#warningButton:hover {{
                background-color: #D97706;
            }}
            
            /* أزرار الخطر */
            QPushButton#dangerButton {{
                background-color: {self.danger_red};
            }}
            
            QPushButton#dangerButton:hover {{
                background-color: #DC2626;
            }}
            
            /* أزرار ثانوية */
            QPushButton#secondaryButton {{
                background-color: {self.gray_medium};
                color: {self.text_dark};
            }}
            
            QPushButton#secondaryButton:hover {{
                background-color: {self.gray_dark};
                color: {self.white};
            }}
        """
    
    def get_input_style(self):
        """أنماط حقول الإدخال العصرية"""
        return f"""
            QLineEdit {{
                font-size: {self.base_font_size}px;
                font-weight: 400;
                color: {self.text_dark};
                background-color: {self.white};
                border: 2px solid {self.gray_medium};
                border-radius: 8px;
                padding: 12px 16px;
                min-height: 24px;
                selection-background-color: {self.primary_blue};
            }}
            
            QLineEdit:focus {{
                border-color: {self.primary_blue};
                background-color: {self.white};
                box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
            }}
            
            QLineEdit:hover {{
                border-color: {self.accent_blue};
            }}
            
            QLineEdit:disabled {{
                background-color: {self.gray_light};
                color: {self.gray_dark};
                border-color: {self.gray_medium};
            }}
            
            QComboBox {{
                font-size: {self.base_font_size}px;
                font-weight: 400;
                color: {self.text_dark};
                background-color: {self.white};
                border: 2px solid {self.gray_medium};
                border-radius: 8px;
                padding: 12px 16px;
                min-width: 180px;
                min-height: 24px;
            }}
            
            QComboBox:focus {{
                border-color: {self.primary_blue};
                box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
            }}
            
            QComboBox:hover {{
                border-color: {self.accent_blue};
            }}
            
            QComboBox::drop-down {{
                border: none;
                width: 30px;
                border-radius: 0px;
            }}
            
            QComboBox::down-arrow {{
                image: none;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 8px solid {self.gray_dark};
                margin: 0 5px;
            }}
            
            QComboBox QAbstractItemView {{
                background-color: {self.white};
                border: 2px solid {self.primary_blue};
                border-radius: 8px;
                selection-background-color: {self.primary_blue};
                selection-color: {self.white};
                padding: 4px;
            }}
            
            QTextEdit {{
                font-size: {self.base_font_size}px;
                font-weight: 400;
                color: {self.text_dark};
                background-color: {self.white};
                border: 2px solid {self.gray_medium};
                border-radius: 8px;
                padding: 12px;
                line-height: 1.5;
                selection-background-color: {self.primary_blue};
            }}
            
            QTextEdit:focus {{
                border-color: {self.primary_blue};
                box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
            }}
            
            QSpinBox {{
                font-size: {self.base_font_size}px;
                font-weight: 400;
                color: {self.text_dark};
                background-color: {self.white};
                border: 2px solid {self.gray_medium};
                border-radius: 8px;
                padding: 10px 12px;
                min-height: 24px;
            }}
            
            QSpinBox:focus {{
                border-color: {self.primary_blue};
                box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
            }}
            
            QSpinBox::up-button, QSpinBox::down-button {{
                background-color: {self.primary_blue};
                border: none;
                border-radius: 4px;
                width: 20px;
                margin: 2px;
            }}
            
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {self.dark_blue};
            }}
            
            QDateEdit {{
                font-size: {self.base_font_size}px;
                color: {self.text_dark};
                background-color: {self.white};
                border: 2px solid {self.gray_medium};
                border-radius: 8px;
                padding: 10px 12px;
                min-height: 24px;
            }}
            
            QDateEdit:focus {{
                border-color: {self.primary_blue};
                box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
            }}
        """
    
    def get_table_style(self):
        """أنماط الجداول العصرية"""
        return f"""
            QTableWidget {{
                font-size: {self.base_font_size}px;
                gridline-color: {self.gray_medium};
                background-color: {self.white};
                alternate-background-color: {self.gray_light};
                border: 1px solid {self.gray_medium};
                border-radius: 12px;
                selection-background-color: {self.primary_blue};
                outline: none;
            }}
            
            QTableWidget::item {{
                padding: 15px 12px;
                border-bottom: 1px solid {self.gray_medium};
                color: {self.text_dark};
                border-radius: 0px;
            }}
            
            QTableWidget::item:selected {{
                background-color: {self.primary_blue};
                color: {self.white};
            }}
            
            QTableWidget::item:hover {{
                background-color: {self.light_blue};
                color: {self.text_dark};
            }}
            
            QHeaderView::section {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.primary_blue}, stop:1 {self.dark_blue});
                color: {self.white};
                padding: 15px 12px;
                border: none;
                font-weight: 600;
                font-size: {self.base_font_size}px;
                text-align: center;
            }}
            
            QHeaderView::section:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.dark_blue}, stop:1 {self.primary_blue});
            }}
            
            QHeaderView::section:first {{
                border-top-left-radius: 12px;
            }}
            
            QHeaderView::section:last {{
                border-top-right-radius: 12px;
            }}
        """
    
    def get_groupbox_style(self):
        """أنماط مربعات المجموعات"""
        return f"""
            QGroupBox {{
                font-size: {self.base_font_size}px;
                font-weight: 600;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px;
                color: #2c3e50;
                font-weight: 700;
            }}
        """
    
    def get_tab_style(self):
        """أنماط التبويبات"""
        return f"""
            QTabWidget::pane {{
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }}
            
            QTabWidget::tab-bar {{
                alignment: center;
            }}
            
            QTabBar::tab {{
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: 600;
                font-size: {self.base_font_size - 1}px;
                min-width: 100px;
            }}
            
            QTabBar::tab:selected {{
                background-color: #3498db;
                color: white;
            }}
            
            QTabBar::tab:hover {{
                background-color: #bdc3c7;
            }}
        """
    
    def get_list_style(self):
        """أنماط القوائم"""
        return f"""
            QListWidget {{
                font-size: {self.base_font_size - 1}px;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
                color: #2c3e50;
                padding: 5px;
            }}
            
            QListWidget::item {{
                padding: 10px;
                border-bottom: 1px solid #f1f2f6;
                border-radius: 4px;
            }}
            
            QListWidget::item:hover {{
                background-color: #ecf0f1;
            }}
            
            QListWidget::item:selected {{
                background-color: #3498db;
                color: white;
            }}
        """
    
    def get_frame_style(self):
        """أنماط الإطارات والعناصر التنظيمية"""
        return f"""
            QFrame#headerFrame {{
                background-color: white;
                border-radius: 8px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }}
            
            QFrame#contentFrame {{
                background-color: white;
                border-radius: 5px;
                margin: 10px;
                padding: 15px;
            }}
            
            QFrame#sidebarFrame {{
                background-color: #2c3e50;
                border-right: 3px solid #34495e;
            }}
        """
    
    def get_modern_sidebar_style(self):
        """نمط الشريط الجانبي العصري المطابق للصورة"""
        return f"""
            QFrame#sidebarFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.sidebar_blue}, stop:1 {self.primary_blue});
                border: none;
                border-radius: 0px;
            }}
            
            QFrame#contentFrame {{
                background-color: {self.white};
                border: none;
                border-radius: 12px;
                margin: 8px;
                padding: 20px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }}
        """
    
    def get_modern_card_style(self):
        """نمط البطاقات العصري"""
        return f"""
            QFrame#cardFrame {{
                background-color: {self.white};
                border: 1px solid {self.gray_medium};
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }}
            
            QFrame#cardFrame:hover {{
                border-color: {self.primary_blue};
                box-shadow: 0 4px 20px rgba(0, 122, 204, 0.15);
            }}
            
            QFrame#dashboardCard {{
                background-color: {self.white};
                border: none;
                border-radius: 16px;
                padding: 24px;
                margin: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            }}
            
            QFrame#statsCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.primary_blue}, stop:1 {self.accent_blue});
                border: none;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }}
        """
    
    def get_complete_style(self):
        """الحصول على النمط الكامل"""
        return (
            self.get_main_style() + 
            self.get_button_style() + 
            self.get_input_style() + 
            self.get_table_style() + 
            self.get_groupbox_style() + 
            self.get_tab_style() + 
            self.get_list_style() + 
            self.get_frame_style() +
            self.get_modern_sidebar_style()
        )
    
    def apply_custom_colors(self, primary_color="#3498db", secondary_color="#2c3e50"):
        """تطبيق ألوان مخصصة"""
        # يمكن استخدام هذه الدالة لتخصيص الألوان
        pass
    
    def set_font_size(self, size):
        """تغيير حجم الخط الأساسي"""
        self.base_font_size = max(10, min(20, size))
