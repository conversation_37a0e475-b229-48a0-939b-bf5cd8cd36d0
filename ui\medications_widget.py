from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QMessageBox, QDialog, QTextEdit, QHeaderView, QFrame,
                            QCompleter, QFormLayout)
from PyQt6.QtCore import Qt, pyqtSignal, QStringListModel
from PyQt6.QtGui import QFont

class MedicationDialog(QDialog):
    """نافذة إضافة/تعديل دواء"""
    
    def __init__(self, medication_model, medication_data=None, parent=None):
        super().__init__(parent)
        self.medication_model = medication_model
        self.medication_data = medication_data
        self.is_edit_mode = medication_data is not None
        self.init_ui()
        
        if self.is_edit_mode:
            self.load_medication_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل دواء" if self.is_edit_mode else "إضافة دواء جديد"
        self.setWindowTitle(title)
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # اسم الدواء
        name_label = QLabel("💊 اسم الدواء:")
        name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        form_layout.addRow(name_label)
        
        self.name_input = QLineEdit()
        self.name_input.setFixedHeight(35)
        self.name_input.setPlaceholderText("أدخل اسم الدواء...")
        form_layout.addRow(self.name_input)
        
        # وصف الدواء
        desc_label = QLabel("📝 وصف الدواء:")
        desc_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        form_layout.addRow(desc_label)
        
        self.description_input = QTextEdit()
        self.description_input.setFixedHeight(80)
        self.description_input.setPlaceholderText("أدخل وصف الدواء (اختياري)...")
        form_layout.addRow(self.description_input)
        
        layout.addLayout(form_layout)
        
        # مساحة فارغة
        layout.addStretch()
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        save_button = QPushButton("💾 حفظ")
        save_button.setFixedHeight(40)
        save_button.setFixedWidth(120)
        save_button.clicked.connect(self.save_medication)
        buttons_layout.addWidget(save_button)
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(40)
        cancel_button.setFixedWidth(120)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                margin-bottom: 5px;
            }
            QLineEdit, QTextEdit {
                padding: 10px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
    
    def load_medication_data(self):
        """تحميل بيانات الدواء للتعديل"""
        if self.medication_data:
            self.name_input.setText(self.medication_data['name'])
            if self.medication_data['description']:
                self.description_input.setPlainText(self.medication_data['description'])
    
    def save_medication(self):
        """حفظ الدواء"""
        name = self.name_input.text().strip()
        description = self.description_input.toPlainText().strip() or None
        
        if not name:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم الدواء")
            return
        
        try:
            if self.is_edit_mode:
                # تحديث الدواء
                success = self.medication_model.update_medication(
                    self.medication_data['id'],
                    name=name,
                    description=description
                )
                if success:
                    QMessageBox.information(self, "نجح", "تم تحديث الدواء بنجاح")
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تحديث الدواء")
            else:
                # إضافة دواء جديد
                medication_id = self.medication_model.add_medication(name, description)
                if medication_id:
                    QMessageBox.information(self, "نجح", "تم إضافة الدواء بنجاح")
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إضافة الدواء")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

class MedicationsWidget(QWidget):
    """ويدجت إدارة الأدوية"""
    
    def __init__(self, medication_model, parent=None):
        super().__init__(parent)
        self.medication_model = medication_model
        self.current_medications = []
        self.init_ui()
        self.load_medications()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # شريط العنوان والبحث
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setSpacing(15)
        
        # العنوان
        title_label = QLabel("💊 إدارة الأدوية")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; border: none; padding: 0;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # مربع البحث
        search_container = QFrame()
        search_container.setStyleSheet("QFrame { border: none; }")
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)
        
        search_label = QLabel("🔍")
        search_label.setStyleSheet("color: #7f8c8d; border: none; padding: 0; margin-right: 5px;")
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في الأدوية...")
        self.search_input.setFixedWidth(250)
        self.search_input.setFixedHeight(35)
        self.search_input.textChanged.connect(self.search_medications)
        search_layout.addWidget(self.search_input)
        
        header_layout.addWidget(search_container)
        
        # زر إضافة دواء
        add_button = QPushButton("➕ إضافة دواء جديد")
        add_button.setFixedHeight(35)
        add_button.setFixedWidth(150)
        add_button.clicked.connect(self.add_medication)
        header_layout.addWidget(add_button)
        
        layout.addWidget(header_frame)
        
        # جدول الأدوية
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان الجدول
        table_title = QLabel("📋 قائمة الأدوية")
        table_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        table_title.setStyleSheet("color: #2c3e50; border: none; padding: 0; margin-bottom: 10px;")
        table_layout.addWidget(table_title)
        
        self.medications_table = QTableWidget()
        self.medications_table.setColumnCount(4)
        self.medications_table.setHorizontalHeaderLabels([
            "اسم الدواء", "الوصف", "عدد الاستخدامات", "تاريخ الإضافة"
        ])
        
        # تعديل عرض الأعمدة
        header = self.medications_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        
        # إعداد الجدول
        self.medications_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.medications_table.setAlternatingRowColors(True)
        self.medications_table.doubleClicked.connect(self.edit_medication)
        self.medications_table.setMinimumHeight(400)
        
        table_layout.addWidget(self.medications_table)
        layout.addWidget(table_frame)
        
        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 10px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)
        
        edit_button = QPushButton("✏️ تعديل")
        edit_button.setFixedHeight(35)
        edit_button.clicked.connect(self.edit_medication)
        buttons_layout.addWidget(edit_button)
        
        delete_button = QPushButton("🗑️ حذف")
        delete_button.setFixedHeight(35)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        delete_button.clicked.connect(self.delete_medication)
        buttons_layout.addWidget(delete_button)
        
        buttons_layout.addStretch()
        
        popular_button = QPushButton("⭐ الأدوية الشائعة")
        popular_button.setFixedHeight(35)
        popular_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
        """)
        popular_button.clicked.connect(self.show_popular_medications)
        buttons_layout.addWidget(popular_button)
        
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.setFixedHeight(35)
        refresh_button.clicked.connect(self.load_medications)
        buttons_layout.addWidget(refresh_button)
        
        layout.addWidget(buttons_frame)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLineEdit {
                padding: 10px 15px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: none;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 13px;
            }
        """)
    
    def load_medications(self):
        """تحميل قائمة الأدوية"""
        try:
            self.current_medications = self.medication_model.search_medications()
            self.update_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الأدوية: {str(e)}")
    
    def update_table(self):
        """تحديث جدول الأدوية"""
        self.medications_table.setRowCount(len(self.current_medications))
        
        for row, medication in enumerate(self.current_medications):
            # اسم الدواء
            self.medications_table.setItem(row, 0, QTableWidgetItem(medication['name']))
            
            # الوصف
            description = medication['description'] or ""
            if len(description) > 100:
                description = description[:100] + "..."
            self.medications_table.setItem(row, 1, QTableWidgetItem(description))
            
            # عدد الاستخدامات
            usage_count = str(medication['usage_count'])
            self.medications_table.setItem(row, 2, QTableWidgetItem(usage_count))
            
            # تاريخ الإضافة
            from datetime import datetime
            created_date = datetime.strptime(medication['created_at'], '%Y-%m-%d %H:%M:%S').strftime('%Y/%m/%d')
            self.medications_table.setItem(row, 3, QTableWidgetItem(created_date))
    
    def search_medications(self):
        """البحث في الأدوية"""
        search_term = self.search_input.text().strip()
        try:
            self.current_medications = self.medication_model.search_medications(search_term)
            self.update_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث: {str(e)}")
    
    def add_medication(self):
        """إضافة دواء جديد"""
        dialog = MedicationDialog(self.medication_model, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_medications()
    
    def edit_medication(self):
        """تعديل دواء"""
        current_row = self.medications_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_medications):
            medication_data = self.current_medications[current_row]
            dialog = MedicationDialog(self.medication_model, medication_data, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_medications()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار دواء للتعديل")
    
    def delete_medication(self):
        """حذف دواء"""
        current_row = self.medications_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_medications):
            medication_data = self.current_medications[current_row]
            
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف الدواء:\n{medication_data['name']}؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    success = self.medication_model.delete_medication(medication_data['id'])
                    if success:
                        QMessageBox.information(self, "نجح", "تم حذف الدواء بنجاح")
                        self.load_medications()
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف الدواء")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار دواء للحذف")
    
    def show_popular_medications(self):
        """عرض الأدوية الشائعة"""
        try:
            popular_meds = self.medication_model.get_popular_medications()
            if popular_meds:
                self.current_medications = popular_meds
                self.update_table()
                self.search_input.clear()
                QMessageBox.information(self, "الأدوية الشائعة", 
                                      f"تم عرض {len(popular_meds)} من الأدوية الأكثر استخداماً")
            else:
                QMessageBox.information(self, "لا توجد بيانات", "لا توجد أدوية شائعة حالياً")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عرض الأدوية الشائعة: {str(e)}")
