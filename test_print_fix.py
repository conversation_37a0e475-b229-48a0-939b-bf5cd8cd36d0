#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات الطباعة والخط
"""

import sys
import os
import json
from datetime import datetime

def test_print_settings():
    """اختبار إعدادات الطباعة"""
    print("🔍 اختبار إعدادات الطباعة...")
    
    # قراءة ملف الإعدادات
    try:
        with open("prescription_settings.json", 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        print("✅ تم تحميل إعدادات الطباعة بنجاح:")
        print(f"   📏 حجم الخط الأساسي: {settings['base_font_size']}px")
        print(f"   🏥 حجم خط اسم العيادة: {settings['clinic_name_size']}px")
        print(f"   👨‍⚕️ حجم خط اسم الطبيب: {settings['doctor_name_size']}px")
        print(f"   💊 حجم خط الأدوية: {settings['medication_size']}px")
        print(f"   📄 حجم الصفحة: {settings['page_size']}")
        print(f"   📐 هوامش الصفحة: {settings['page_margins']}mm")
        print(f"   🔤 نوع الخط: {settings['font_family']}")
        
        # التحقق من الأحجام المناسبة
        base_size = settings['base_font_size']
        if 12 <= base_size <= 16:
            print("✅ حجم الخط الأساسي مناسب للطباعة")
        else:
            print("⚠️ حجم الخط الأساسي قد يكون غير مناسب للطباعة")
            
        if settings['page_size'] in ['A4', 'A5']:
            print(f"✅ حجم الصفحة {settings['page_size']} مناسب")
        else:
            print("⚠️ حجم الصفحة قد يكون غير مناسب")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة إعدادات الطباعة: {e}")
        return False

def test_font_calculations():
    """اختبار حسابات الخط"""
    print("\n🔍 اختبار حسابات أحجام الخط...")
    
    try:
        with open("prescription_settings.json", 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        base_size = settings['base_font_size']
        
        # حساب الأحجام المختلفة
        print(f"   📏 الخط الأساسي: {base_size}px")
        print(f"   📏 خط كبير (×1.3): {int(base_size * 1.3)}px")
        print(f"   📏 خط متوسط (×1.1): {int(base_size * 1.1)}px")
        print(f"   📏 خط صغير (×0.9): {int(base_size * 0.9)}px")
        print(f"   📏 خط صغير جداً (×0.8): {int(base_size * 0.8)}px")
        
        # تحديد النسب المناسبة لكل عنصر
        clinic_ratio = settings['clinic_name_size'] / base_size
        doctor_ratio = settings['doctor_name_size'] / base_size
        prescription_ratio = settings['prescription_size'] / base_size
        medication_ratio = settings['medication_size'] / base_size
        
        print(f"\n📊 نسب الأحجام:")
        print(f"   🏥 العيادة: {clinic_ratio:.1f}x")
        print(f"   👨‍⚕️ الطبيب: {doctor_ratio:.1f}x")
        print(f"   💊 الوصفة: {prescription_ratio:.1f}x") 
        print(f"   💊 الأدوية: {medication_ratio:.1f}x")
        
        # التحقق من النسب المناسبة
        if 1.4 <= clinic_ratio <= 2.0:
            print("✅ نسبة خط العيادة مناسبة")
        else:
            print("⚠️ نسبة خط العيادة قد تحتاج تعديل")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حساب أحجام الخط: {e}")
        return False

def test_page_layout():
    """اختبار تخطيط الصفحة"""
    print("\n🔍 اختبار تخطيط الصفحة...")
    
    try:
        with open("prescription_settings.json", 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        page_size = settings['page_size']
        margins = settings['page_margins']
        spacing = settings['section_spacing']
        
        print(f"   📄 حجم الصفحة: {page_size}")
        print(f"   📐 الهوامش: {margins}mm")
        print(f"   📏 المسافات: {spacing}px")
        
        # حساب المساحة المتاحة تقريبياً
        if page_size == 'A4':
            page_width = 210 - (margins * 2)  # mm
            page_height = 297 - (margins * 2)  # mm
            print(f"   📐 المساحة المتاحة: {page_width}mm × {page_height}mm")
        elif page_size == 'A5':
            page_width = 148 - (margins * 2)  # mm
            page_height = 210 - (margins * 2)  # mm
            print(f"   📐 المساحة المتاحة: {page_width}mm × {page_height}mm")
        
        print("✅ تخطيط الصفحة يبدو مناسباً")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تخطيط الصفحة: {e}")
        return False

def generate_sample_html():
    """إنشاء عينة HTML للاختبار"""
    print("\n🔍 إنشاء عينة HTML للاختبار...")
    
    try:
        with open("prescription_settings.json", 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="utf-8">
            <title>اختبار الطباعة</title>
            <style>
                @page {{
                    size: {settings['page_size']};
                    margin: {settings['page_margins']}mm;
                }}
                body {{
                    font-family: '{settings['font_family']}', sans-serif;
                    font-size: {settings['base_font_size']}px;
                    line-height: 1.6;
                    margin: 0;
                    padding: {settings['page_margins']}mm;
                    direction: rtl;
                    color: #2c3e50;
                    background: #ffffff;
                }}
                .header {{
                    text-align: center;
                    border: 3px solid #3498db;
                    padding: 20px;
                    margin-bottom: 20px;
                    background: #3498db;
                    border-radius: 15px;
                    color: white;
                }}
                .clinic-name {{
                    font-size: {settings['clinic_name_size']}px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }}
                .doctor-name {{
                    font-size: {settings['doctor_name_size']}px;
                    margin-bottom: 10px;
                }}
                .prescription-title {{
                    font-size: {settings['prescription_size']}px;
                    font-weight: bold;
                    color: #e74c3c;
                    margin: 20px 0;
                    text-align: center;
                }}
                .medication {{
                    font-size: {settings['medication_size']}px;
                    margin: 10px 0;
                    padding: 10px;
                    background: #f8f9fa;
                    border-right: 4px solid #e74c3c;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="clinic-name">🏥 عيادة الاختبار الطبية</div>
                <div class="doctor-name">👨‍⚕️ د. أحمد محمد</div>
                <div>📍 الرياض - المملكة العربية السعودية | 📞 01234567890</div>
            </div>
            
            <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <div style="font-size: {int(settings['base_font_size'] * 1.2)}px; font-weight: bold; margin-bottom: 10px;">
                    👤 معلومات المريض
                </div>
                <div>الاسم: أحمد علي محمد</div>
                <div>العمر: 35 سنة</div>
                <div>الهاتف: 0501234567</div>
                <div>تاريخ الزيارة: {datetime.now().strftime('%Y/%m/%d')}</div>
            </div>
            
            <div class="prescription-title">💊 الوصفة الطبية</div>
            
            <div style="background: #fdf2f2; padding: 20px; border-radius: 10px; border: 3px solid #e74c3c;">
                <div class="medication">1. باراسيتامول 500mg - قرص كل 6 ساعات</div>
                <div class="medication">2. إيبوبروفين 400mg - قرص كل 8 ساعات مع الطعام</div>
                <div class="medication">3. أوميبرازول 20mg - كبسولة واحدة قبل الإفطار</div>
                <div class="medication">4. فيتامين د 2000 وحدة - قرص يومياً</div>
            </div>
            
            <div style="background: #fff3cd; padding: 15px; border-radius: 10px; margin: 20px 0; border: 2px solid #ffc107;">
                <strong>⚠️ تنبيه:</strong> هذه الوصفة صالحة لمدة شهر واحد من تاريخ الإصدار
            </div>
            
            <div style="margin-top: 40px; text-align: center;">
                <div style="border-top: 3px solid #2c3e50; width: 200px; margin: 0 auto 15px auto;"></div>
                <div style="font-weight: bold;">توقيع الطبيب المعالج</div>
            </div>
        </body>
        </html>
        """
        
        with open("test_prescription_sample.html", 'w', encoding='utf-8') as f:
            f.write(html)
            
        print("✅ تم إنشاء ملف اختبار الطباعة: test_prescription_sample.html")
        print("   يمكنك فتح هذا الملف في المتصفح لمعاينة الطباعة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء عينة HTML: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🏥 اختبار إصلاحات الطباعة والخط")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # اختبار الإعدادات
    if test_print_settings():
        tests_passed += 1
    
    # اختبار حسابات الخط
    if test_font_calculations():
        tests_passed += 1
    
    # اختبار تخطيط الصفحة
    if test_page_layout():
        tests_passed += 1
    
    # إنشاء عينة HTML
    if generate_sample_html():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} نجح")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ الطباعة جاهزة للاستخدام")
    else:
        print("⚠️ بعض الاختبارات فشلت، يرجى مراجعة الأخطاء")
    
    print("\n📋 توصيات للاستخدام:")
    print("   • استخدم A4 للطباعة العادية")
    print("   • استخدم A5 للوصفات الصغيرة")
    print("   • تأكد من ضبط الطابعة على جودة عالية")
    print("   • اختبر الطباعة قبل الاستخدام الفعلي")

if __name__ == "__main__":
    main()
