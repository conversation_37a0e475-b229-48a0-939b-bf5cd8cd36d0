import re
from typing import List, Dict, Tuple

class PrescriptionFormatter:
    """منسق الوصفات الطبية التلقائي"""
    
    def __init__(self):
        # قاموس الأدوية الشائعة مع جرعاتها
        self.common_medications = {
            # مسكنات الألم
            'paracetamol': {'ar': 'باراسيتامول', 'doses': ['500mg', '1000mg'], 'frequency': 'كل 6-8 ساعات'},
            'ibuprofen': {'ar': 'إيبوبروفين', 'doses': ['200mg', '400mg', '600mg'], 'frequency': 'كل 6-8 ساعات'},
            'aspirin': {'ar': 'أسبرين', 'doses': ['75mg', '100mg', '300mg'], 'frequency': 'يومياً'},
            'diclofenac': {'ar': 'ديكلوفيناك', 'doses': ['50mg', '75mg'], 'frequency': 'كل 8-12 ساعة'},
            
            # مضادات حيوية
            'amoxicillin': {'ar': 'أموكسيسيلين', 'doses': ['250mg', '500mg', '875mg'], 'frequency': 'كل 8 ساعات'},
            'azithromycin': {'ar': 'أزيثروميسين', 'doses': ['250mg', '500mg'], 'frequency': 'يومياً'},
            'cephalexin': {'ar': 'سيفالكسين', 'doses': ['250mg', '500mg'], 'frequency': 'كل 6 ساعات'},
            'ciprofloxacin': {'ar': 'سيبروفلوكساسين', 'doses': ['250mg', '500mg'], 'frequency': 'كل 12 ساعة'},
            
            # أدوية الضغط
            'amlodipine': {'ar': 'أملوديبين', 'doses': ['5mg', '10mg'], 'frequency': 'يومياً'},
            'lisinopril': {'ar': 'ليسينوبريل', 'doses': ['5mg', '10mg', '20mg'], 'frequency': 'يومياً'},
            'metoprolol': {'ar': 'ميتوبرولول', 'doses': ['25mg', '50mg', '100mg'], 'frequency': 'كل 12 ساعة'},
            
            # أدوية السكري
            'metformin': {'ar': 'ميتفورمين', 'doses': ['500mg', '850mg', '1000mg'], 'frequency': 'مع الوجبات'},
            'glimepiride': {'ar': 'جليميبيرايد', 'doses': ['1mg', '2mg', '4mg'], 'frequency': 'قبل الإفطار'},
            
            # فيتامينات
            'vitamin_d': {'ar': 'فيتامين د', 'doses': ['1000IU', '2000IU', '5000IU'], 'frequency': 'يومياً'},
            'vitamin_b12': {'ar': 'فيتامين ب12', 'doses': ['1000mcg'], 'frequency': 'يومياً'},
            'folic_acid': {'ar': 'حمض الفوليك', 'doses': ['5mg'], 'frequency': 'يومياً'},
            
            # أدوية المعدة
            'omeprazole': {'ar': 'أوميبرازول', 'doses': ['20mg', '40mg'], 'frequency': 'قبل الإفطار'},
            'ranitidine': {'ar': 'رانيتيدين', 'doses': ['150mg', '300mg'], 'frequency': 'كل 12 ساعة'},
            'simethicone': {'ar': 'سيميثيكون', 'doses': ['40mg', '80mg'], 'frequency': 'بعد الوجبات'}
        }
        
        # أنماط التعرف على الأدوية
        self.medication_patterns = [
            r'([a-zA-Z]+)\s*(\d+)\s*(mg|mcg|g|ml|iu|وحدة|مجم|جم|مل)',
            r'([أ-ي]+)\s*(\d+)\s*(مجم|جم|مل|وحدة)',
            r'([a-zA-Z\u0600-\u06FF]+)\s*(\d+)',
        ]
        
        # أنماط التكرار
        self.frequency_patterns = {
            r'(\d+)\s*مرة?\s*(يوم|يومي|يومياً)': 'مرة يومياً',
            r'كل\s*(\d+)\s*ساعة?': 'كل {} ساعة',
            r'(\d+)\s*مرات?\s*يومياً?': '{} مرات يومياً',
            r'مع\s*الوجبات?': 'مع الوجبات',
            r'قبل\s*الوجبات?': 'قبل الوجبات',
            r'بعد\s*الوجبات?': 'بعد الوجبات',
            r'عند\s*الحاجة': 'عند الحاجة',
            r'صباحاً?': 'صباحاً',
            r'مساءً?': 'مساءاً'
        }
    
    def format_prescription(self, text: str) -> str:
        """تنسيق الوصفة الطبية تلقائياً"""
        if not text.strip():
            return text
        
        # تنظيف النص
        cleaned_text = self._clean_text(text)
        
        # تقسيم النص إلى أسطر
        lines = cleaned_text.split('\n')
        
        # معالجة كل سطر
        formatted_lines = []
        counter = 1
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # محاولة تنسيق السطر كدواء
            formatted_line = self._format_medication_line(line, counter)
            if formatted_line:
                formatted_lines.append(formatted_line)
                counter += 1
            else:
                # إضافة السطر كما هو إذا لم يكن دواء
                formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
    
    def _clean_text(self, text: str) -> str:
        """تنظيف النص من الأحرف غير المرغوبة"""
        # إزالة الأرقام في بداية الأسطر
        text = re.sub(r'^\d+[\.\-\)]\s*', '', text, flags=re.MULTILINE)
        
        # إزالة النقاط والشرطات الزائدة
        text = re.sub(r'^[\.\-\*\+]\s*', '', text, flags=re.MULTILINE)
        
        # توحيد المسافات
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _format_medication_line(self, line: str, counter: int) -> str:
        """تنسيق سطر دواء واحد"""
        # البحث عن نمط الدواء
        medication_info = self._extract_medication_info(line)
        
        if not medication_info:
            return None
        
        name = medication_info.get('name', '')
        dose = medication_info.get('dose', '')
        unit = medication_info.get('unit', '')
        frequency = medication_info.get('frequency', '')
        duration = medication_info.get('duration', '')
        instructions = medication_info.get('instructions', '')
        
        # تنسيق السطر
        formatted_parts = [f"{counter}."]
        
        # اسم الدواء
        if name:
            # البحث عن الترجمة العربية
            arabic_name = self._get_arabic_name(name)
            if arabic_name and arabic_name != name:
                formatted_parts.append(f"{arabic_name} ({name})")
            else:
                formatted_parts.append(name)
        
        # الجرعة
        if dose and unit:
            formatted_parts.append(f"{dose} {unit}")
        elif dose:
            formatted_parts.append(dose)
        
        # التكرار
        if frequency:
            formatted_parts.append(f"- {frequency}")
        
        # المدة
        if duration:
            formatted_parts.append(f"لمدة {duration}")
        
        # تعليمات إضافية
        if instructions:
            formatted_parts.append(f"({instructions})")
        
        return ' '.join(formatted_parts)
    
    def _extract_medication_info(self, line: str) -> Dict[str, str]:
        """استخراج معلومات الدواء من السطر"""
        info = {}
        
        # البحث عن اسم الدواء والجرعة
        for pattern in self.medication_patterns:
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                info['name'] = match.group(1).strip()
                if len(match.groups()) >= 2:
                    info['dose'] = match.group(2).strip()
                if len(match.groups()) >= 3:
                    info['unit'] = match.group(3).strip()
                break
        
        # البحث عن التكرار
        for pattern, replacement in self.frequency_patterns.items():
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                if '{}' in replacement:
                    info['frequency'] = replacement.format(match.group(1))
                else:
                    info['frequency'] = replacement
                break
        
        # البحث عن المدة
        duration_match = re.search(r'لمدة\s*(\d+)\s*(يوم|أسبوع|شهر)', line, re.IGNORECASE)
        if duration_match:
            info['duration'] = f"{duration_match.group(1)} {duration_match.group(2)}"
        
        # البحث عن تعليمات إضافية
        instruction_patterns = [
            r'(مع الطعام|بدون طعام|على معدة فارغة)',
            r'(قبل النوم|عند الاستيقاظ)',
            r'(حسب الحاجة|عند اللزوم)'
        ]
        
        for pattern in instruction_patterns:
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                info['instructions'] = match.group(1)
                break
        
        return info
    
    def _get_arabic_name(self, english_name: str) -> str:
        """الحصول على الاسم العربي للدواء"""
        english_name_lower = english_name.lower()
        
        for key, value in self.common_medications.items():
            if key == english_name_lower or english_name_lower in key:
                return value['ar']
        
        return english_name
    
    def suggest_dose_and_frequency(self, medication_name: str) -> Dict[str, List[str]]:
        """اقتراح الجرعة والتكرار للدواء"""
        medication_name_lower = medication_name.lower()
        
        for key, value in self.common_medications.items():
            if key == medication_name_lower or medication_name_lower in key:
                return {
                    'doses': value['doses'],
                    'frequency': [value['frequency']],
                    'arabic_name': value['ar']
                }
        
        return {'doses': [], 'frequency': [], 'arabic_name': medication_name}
    
    def validate_prescription(self, text: str) -> List[Dict[str, str]]:
        """التحقق من صحة الوصفة وإرجاع التحذيرات"""
        warnings = []
        lines = text.split('\n')
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            # التحقق من وجود جرعة
            if not re.search(r'\d+\s*(mg|mcg|g|ml|iu|وحدة|مجم|جم|مل)', line, re.IGNORECASE):
                warnings.append({
                    'line': i,
                    'type': 'missing_dose',
                    'message': f'السطر {i}: لا توجد جرعة محددة',
                    'suggestion': 'يرجى إضافة الجرعة (مثل: 500 مجم)'
                })
            
            # التحقق من وجود تكرار
            has_frequency = False
            for pattern in self.frequency_patterns.keys():
                if re.search(pattern, line, re.IGNORECASE):
                    has_frequency = True
                    break
            
            if not has_frequency:
                warnings.append({
                    'line': i,
                    'type': 'missing_frequency',
                    'message': f'السطر {i}: لا يوجد تكرار محدد',
                    'suggestion': 'يرجى إضافة التكرار (مثل: مرة يومياً، كل 8 ساعات)'
                })
        
        return warnings
    
    def auto_complete_prescription(self, partial_text: str) -> List[str]:
        """إكمال الوصفة تلقائياً"""
        suggestions = []
        
        # البحث عن أدوية مشابهة
        for key, value in self.common_medications.items():
            if partial_text.lower() in key or partial_text.lower() in value['ar'].lower():
                # إنشاء اقتراح كامل
                suggestion = f"{value['ar']} {value['doses'][0]} - {value['frequency']}"
                suggestions.append(suggestion)
        
        return suggestions[:5]  # أول 5 اقتراحات
