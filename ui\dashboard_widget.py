from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QFrame, QGridLayout, QPushButton, QScrollArea,
                            QProgressBar, QListWidget, QListWidgetItem, QMessageBox)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QColor, QPalette, QLinearGradient, QBrush
from datetime import datetime, timedelta
import sqlite3
from styles.style_manager import StyleManager

class StatCard(QFrame):
    """بطاقة إحصائية عصرية"""
    
    def __init__(self, title, value, icon, color, parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
        self.init_ui()
        self.setup_animation()
    
    def init_ui(self):
        """إعداد واجهة البطاقة العصرية"""
        # إزالة الحجم الثابت لجعلها مرنة
        self.setMinimumSize(200, 120)
        self.setMaximumSize(300, 180)
        self.setObjectName("dashboardCard")
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(15)

        # الجزء العلوي - الأيقونة والقيمة
        top_layout = QHBoxLayout()

        # الأيقونة في دائرة ملونة
        icon_frame = QFrame()
        icon_frame.setFixedSize(50, 50)
        icon_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {self.color};
                border-radius: 25px;
                border: none;
            }}
        """)
        
        icon_layout = QVBoxLayout(icon_frame)
        icon_layout.setContentsMargins(0, 0, 0, 0)
        
        self.icon_label = QLabel(self.icon)
        self.icon_label.setFont(QFont("Segoe UI Emoji", 16))
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.icon_label.setStyleSheet("color: white; background: transparent;")
        icon_layout.addWidget(self.icon_label)
        
        # القيمة
        self.value_label = QLabel(str(self.value))
        self.value_label.setFont(QFont("Segoe UI", 24, QFont.Weight.Bold))
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.value_label.setStyleSheet(f"color: {self.color}; background: transparent;")

        top_layout.addWidget(icon_frame)
        top_layout.addStretch()
        top_layout.addWidget(self.value_label)

        # العنوان
        self.title_label = QLabel(self.title)
        self.title_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Medium))
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.title_label.setStyleSheet("color: #64748B; background: transparent;")

        layout.addLayout(top_layout)
        layout.addWidget(self.title_label)
        layout.addStretch()
        
        # شريط التقدم (اختياري)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                background-color: rgba(255, 255, 255, 0.3);
                border-radius: 5px;
                height: 8px;
            }
            QProgressBar::chunk {
                background-color: white;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.progress_bar)
    
    def darken_color(self, color):
        """تغميق اللون للتدرج"""
        color_map = {
            "#3498db": "#2980b9",
            "#e74c3c": "#c0392b", 
            "#27ae60": "#229954",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad",
            "#1abc9c": "#16a085"
        }
        return color_map.get(color, color)
    
    def setup_animation(self):
        """إعداد الحركة"""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def enterEvent(self, event):
        """عند دخول الماوس"""
        self.animate_scale(1.05)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """عند خروج الماوس"""
        self.animate_scale(1.0)
        super().leaveEvent(event)
    
    def animate_scale(self, scale):
        """تحريك التكبير"""
        current_rect = self.geometry()
        center = current_rect.center()
        new_width = int(200 * scale)
        new_height = int(120 * scale)
        new_rect = QRect(0, 0, new_width, new_height)
        new_rect.moveCenter(center)
        
        self.animation.setStartValue(current_rect)
        self.animation.setEndValue(new_rect)
        self.animation.start()
    
    def update_value(self, new_value):
        """تحديث القيمة"""
        old_value = self.value
        self.value = new_value

        # تحديث QLabel القيمة مباشرة
        if hasattr(self, 'value_label'):
            self.value_label.setText(str(new_value))
            # تحديث النص مع التأكد من الظهور
            self.value_label.setVisible(True)
            self.value_label.update()
        else:
            # البحث عن QLabel الذي يحتوي على القيمة وتحديثه
            labels = self.findChildren(QLabel)
            for label in labels:
                # البحث عن QLabel الذي يحتوي على رقم أو القيمة القديمة
                text = label.text()
                if (text.isdigit() and text == str(old_value)) or text == str(old_value):
                    label.setText(str(new_value))
                    label.setVisible(True)
                    label.update()
                    break

        # إجبار إعادة الرسم
        self.update()
        self.repaint()

class DashboardWidget(QWidget):
    """لوحة التحكم الرئيسية"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.stat_cards = {}
        self.style_manager = StyleManager()
        self.init_ui()
        self.load_statistics()
        
        # تحديث تلقائي كل 30 ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_statistics)
        self.timer.start(30000)
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        # تطبيق الأنماط
        combined_style = (
            self.style_manager.get_main_style() +
            self.style_manager.get_button_style() +
            self.style_manager.get_frame_style() +
            self.get_dashboard_style()
        )
        self.setStyleSheet(combined_style)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان الرئيسي
        self.create_header(layout)
        
        # منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setObjectName("scrollArea")
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(20)
        
        # إحصائيات سريعة (مراجعين اليوم والإجمالي)
        self.create_quick_stats_section(scroll_layout)

        # أزرار الإجراءات السريعة
        self.create_quick_actions(scroll_layout)

        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)
    
    def create_header(self, layout):
        """إنشاء العنوان الرئيسي"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                padding: 20px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        
        # العنوان
        title_label = QLabel("🏥 لوحة التحكم الرئيسية")
        title_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white; border: none; padding: 0;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)
        
        # معلومات المستخدم والوقت
        info_layout = QHBoxLayout()
        
        user_info = QLabel(f"مرحباً، {self.user_data['full_name']}")
        user_info.setStyleSheet("color: rgba(255, 255, 255, 0.9); border: none; font-size: 16px;")
        info_layout.addWidget(user_info)
        
        info_layout.addStretch()
        
        self.time_label = QLabel()
        self.time_label.setStyleSheet("color: rgba(255, 255, 255, 0.9); border: none; font-size: 16px;")
        self.update_time()
        info_layout.addWidget(self.time_label)
        
        header_layout.addLayout(info_layout)
        layout.addWidget(header_frame)
        
        # مؤقت تحديث الوقت
        time_timer = QTimer()
        time_timer.timeout.connect(self.update_time)
        time_timer.start(1000)
    
    def create_stats_section(self, layout):
        """إنشاء قسم الإحصائيات"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 15px;
                border: 1px solid #e9ecef;
                padding: 20px;
            }
        """)
        stats_layout = QVBoxLayout(stats_frame)
        
        # عنوان القسم
        section_title = QLabel("📊 الإحصائيات السريعة")
        section_title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        section_title.setStyleSheet("color: #2c3e50; border: none; margin-bottom: 15px;")
        stats_layout.addWidget(section_title)
        
        # شبكة البطاقات مرنة
        cards_layout = QGridLayout()
        cards_layout.setSpacing(15)
        # جعل الشبكة قابلة للتمدد
        cards_layout.setColumnStretch(0, 1)
        cards_layout.setColumnStretch(1, 1)
        cards_layout.setColumnStretch(2, 1)
        cards_layout.setColumnStretch(3, 1)
        
        # بطاقات الإحصائيات (ستملأ بالبيانات لاحقاً)
        self.stat_cards['patients'] = StatCard("إجمالي المرضى", "0", "👥", "#3498db")
        self.stat_cards['today_visits'] = StatCard("زيارات اليوم", "0", "📅", "#27ae60")
        self.stat_cards['week_visits'] = StatCard("زيارات الأسبوع", "0", "📈", "#e74c3c")
        self.stat_cards['medications'] = StatCard("الأدوية", "0", "💊", "#f39c12")

        # التأكد من ظهور النصوص
        for card in self.stat_cards.values():
            card.setVisible(True)
            card.update()
            # إجبار إعادة الرسم
            card.repaint()
        
        # ترتيب البطاقات في الشبكة بشكل مرن
        row = 0
        col = 0
        for card in [self.stat_cards['patients'], self.stat_cards['today_visits'], 
                    self.stat_cards['week_visits'], self.stat_cards['medications']]:
            cards_layout.addWidget(card, row, col)
            col += 1
            if col >= 4:  # كحد أقصى 4 أعمدة
                col = 0
                row += 1
        
        # بطاقات إضافية للطبيب
        if self.user_data['role'] == 'doctor':
            self.stat_cards['prescriptions'] = StatCard("الوصفات", "0", "📝", "#9b59b6")
            self.stat_cards['diagnoses'] = StatCard("التشخيصات", "0", "🔍", "#1abc9c")
            
            if col >= 2:  # إذا كان هناك مساحة في نفس الصف
                cards_layout.addWidget(self.stat_cards['prescriptions'], row, col)
                cards_layout.addWidget(self.stat_cards['diagnoses'], row, col + 1)
            else:  # صف جديد
                row += 1
                cards_layout.addWidget(self.stat_cards['prescriptions'], row, 0)
                cards_layout.addWidget(self.stat_cards['diagnoses'], row, 1)
        
        stats_layout.addLayout(cards_layout)
        layout.addWidget(stats_frame)
    
    def create_charts_section(self, layout):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = QFrame()
        charts_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 15px;
                border: 1px solid #e9ecef;
                padding: 20px;
            }
        """)
        charts_layout = QVBoxLayout(charts_frame)
        
        # عنوان القسم
        section_title = QLabel("📈 الرسوم البيانية والتحليلات")
        section_title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        section_title.setStyleSheet("color: #2c3e50; border: none; margin-bottom: 15px;")
        charts_layout.addWidget(section_title)
        
        # تخطيط أفقي للرسوم
        charts_row = QHBoxLayout()
        
        # الأدوية الأكثر استخداماً
        self.create_top_medications_chart(charts_row)
        
        # التشخيصات الأكثر شيوعاً
        self.create_top_diagnoses_chart(charts_row)
        
        charts_layout.addLayout(charts_row)
        layout.addWidget(charts_frame)
    
    def create_top_medications_chart(self, layout):
        """إنشاء قائمة الأدوية الأكثر استخداماً"""
        med_frame = QFrame()
        med_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 10px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        med_layout = QVBoxLayout(med_frame)
        
        med_title = QLabel("💊 الأدوية الأكثر استخداماً")
        med_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        med_title.setStyleSheet("color: #2c3e50; border: none; margin-bottom: 10px;")
        med_layout.addWidget(med_title)
        
        self.medications_list = QListWidget()
        self.medications_list.setMaximumHeight(200)
        self.medications_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f1f2f6;
                border-radius: 4px;
            }
            QListWidget::item:hover {
                background-color: #ecf0f1;
            }
        """)
        med_layout.addWidget(self.medications_list)
        
        layout.addWidget(med_frame)
    
    def create_top_diagnoses_chart(self, layout):
        """إنشاء قائمة التشخيصات الأكثر شيوعاً"""
        diag_frame = QFrame()
        diag_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 10px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        diag_layout = QVBoxLayout(diag_frame)
        
        diag_title = QLabel("🔍 التشخيصات الأكثر شيوعاً")
        diag_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        diag_title.setStyleSheet("color: #2c3e50; border: none; margin-bottom: 10px;")
        diag_layout.addWidget(diag_title)
        
        self.diagnoses_list = QListWidget()
        self.diagnoses_list.setMaximumHeight(200)
        self.diagnoses_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f1f2f6;
                border-radius: 4px;
            }
            QListWidget::item:hover {
                background-color: #ecf0f1;
            }
        """)
        diag_layout.addWidget(self.diagnoses_list)
        
        layout.addWidget(diag_frame)
    
    def create_recent_activities(self, layout):
        """إنشاء قسم الأنشطة الأخيرة"""
        activities_frame = QFrame()
        activities_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 15px;
                border: 1px solid #e9ecef;
                padding: 20px;
            }
        """)
        activities_layout = QVBoxLayout(activities_frame)
        
        # عنوان القسم
        section_title = QLabel("🕒 الأنشطة الأخيرة")
        section_title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        section_title.setStyleSheet("color: #2c3e50; border: none; margin-bottom: 15px;")
        activities_layout.addWidget(section_title)
        
        # قائمة الأنشطة
        self.activities_list = QListWidget()
        self.activities_list.setMaximumHeight(150)
        self.activities_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 6px;
                background-color: #f8f9fa;
                padding: 10px;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #e9ecef;
                border-radius: 6px;
                margin-bottom: 5px;
                background-color: white;
            }
            QListWidget::item:hover {
                background-color: #ecf0f1;
            }
        """)
        activities_layout.addWidget(self.activities_list)
        
        layout.addWidget(activities_frame)

    def create_quick_actions(self, layout):
        """إنشاء أزرار الإجراءات السريعة"""
        actions_frame = QFrame()
        actions_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 15px;
                border: 1px solid #e9ecef;
                padding: 20px;
            }
        """)
        actions_layout = QVBoxLayout(actions_frame)

        # عنوان القسم
        section_title = QLabel("⚡ إجراءات سريعة")
        section_title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        section_title.setStyleSheet("color: #2c3e50; border: none; margin-bottom: 15px;")
        actions_layout.addWidget(section_title)

        # شبكة الأزرار
        buttons_layout = QGridLayout()
        buttons_layout.setSpacing(15)

        # زر إضافة مريض جديد
        add_patient_btn = QPushButton("👤 مريض جديد")
        add_patient_btn.setMinimumSize(150, 60)
        add_patient_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        add_patient_btn.clicked.connect(self.add_new_patient)
        buttons_layout.addWidget(add_patient_btn, 0, 0)

        # زر زيارة جديدة
        add_visit_btn = QPushButton("📋 زيارة جديدة")
        add_visit_btn.setMinimumSize(150, 60)
        add_visit_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_visit_btn.clicked.connect(self.add_new_visit)
        buttons_layout.addWidget(add_visit_btn, 0, 1)

        # زر البحث السريع
        search_btn = QPushButton("🔍 بحث سريع")
        search_btn.setMinimumSize(150, 60)
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        search_btn.clicked.connect(self.quick_search)
        buttons_layout.addWidget(search_btn, 0, 2)

        # زر التقارير
        reports_btn = QPushButton("📊 تقارير")
        reports_btn.setMinimumSize(150, 60)
        reports_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        reports_btn.clicked.connect(self.open_reports)
        buttons_layout.addWidget(reports_btn, 1, 0)

        # زر النسخ الاحتياطي
        backup_btn = QPushButton("💾 نسخ احتياطي")
        backup_btn.setMinimumSize(150, 60)
        backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        backup_btn.clicked.connect(self.create_backup)
        buttons_layout.addWidget(backup_btn, 1, 1)

        # زر الإعدادات
        settings_btn = QPushButton("⚙️ إعدادات")
        settings_btn.setMinimumSize(150, 60)
        settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #34495e;
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2c3e50;
            }
        """)
        settings_btn.clicked.connect(self.open_settings)
        buttons_layout.addWidget(settings_btn, 1, 2)

        actions_layout.addLayout(buttons_layout)
        layout.addWidget(actions_frame)

    def add_new_patient(self):
        """إضافة مريض جديد"""
        # البحث عن النافذة الرئيسية
        main_window = self.get_main_window()
        if main_window and hasattr(main_window, 'show_patients'):
            main_window.show_patients()

    def add_new_visit(self):
        """إضافة زيارة جديدة"""
        main_window = self.get_main_window()
        if main_window and hasattr(main_window, 'show_patients'):
            main_window.show_patients()

    def quick_search(self):
        """البحث السريع"""
        main_window = self.get_main_window()
        if main_window and hasattr(main_window, 'show_patients'):
            main_window.show_patients()

    def open_reports(self):
        """فتح التقارير"""
        main_window = self.get_main_window()
        if main_window and hasattr(main_window, 'show_reports'):
            main_window.show_reports()

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        main_window = self.get_main_window()
        if main_window and hasattr(main_window, 'create_backup'):
            main_window.create_backup()

    def open_settings(self):
        """فتح الإعدادات"""
        main_window = self.get_main_window()
        if main_window and hasattr(main_window, 'show_settings'):
            main_window.show_settings()

    def get_main_window(self):
        """الحصول على النافذة الرئيسية"""
        widget = self
        while widget:
            if hasattr(widget, 'show_patients'):  # التحقق من أنها النافذة الرئيسية
                return widget
            widget = widget.parent()
        return None

    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%A, %d %B %Y - %H:%M:%S")
        self.time_label.setText(current_time)
    
    def load_statistics(self):
        """تحميل الإحصائيات من قاعدة البيانات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # إجمالي المرضى
            cursor.execute("SELECT COUNT(*) FROM patients")
            total_patients = cursor.fetchone()[0]
            self.stat_cards['patients'].update_value(total_patients)
            
            # زيارات اليوم
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute("SELECT COUNT(*) FROM visits WHERE DATE(visit_date) = ?", (today,))
            today_visits = cursor.fetchone()[0]
            self.stat_cards['today_visits'].update_value(today_visits)
            
            # زيارات الأسبوع
            week_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            cursor.execute("SELECT COUNT(*) FROM visits WHERE visit_date >= ?", (week_ago,))
            week_visits = cursor.fetchone()[0]
            self.stat_cards['week_visits'].update_value(week_visits)
            
            # إجمالي الأدوية
            cursor.execute("SELECT COUNT(*) FROM medications")
            total_medications = cursor.fetchone()[0]
            self.stat_cards['medications'].update_value(total_medications)
            
            # الأدوية الأكثر استخداماً
            cursor.execute("""
                SELECT name, usage_count FROM medications 
                ORDER BY usage_count DESC LIMIT 5
            """)
            top_medications = cursor.fetchall()
            
            self.medications_list.clear()
            for i, (name, count) in enumerate(top_medications, 1):
                item = QListWidgetItem(f"{i}. {name} ({count} مرة)")
                self.medications_list.addItem(item)
            
            # تحديث الأنشطة الأخيرة
            self.load_recent_activities(cursor)
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")
    
    def load_recent_activities(self, cursor):
        """تحميل الأنشطة الأخيرة"""
        try:
            # آخر الزيارات
            cursor.execute("""
                SELECT p.full_name, v.visit_date, v.created_at
                FROM visits v
                JOIN patients p ON v.patient_id = p.id
                ORDER BY v.created_at DESC
                LIMIT 5
            """)
            recent_visits = cursor.fetchall()
            
            self.activities_list.clear()
            for name, _, created_at in recent_visits:
                created_time = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                time_str = created_time.strftime('%H:%M')
                activity_text = f"📋 زيارة جديدة: {name} - {time_str}"
                item = QListWidgetItem(activity_text)
                self.activities_list.addItem(item)
                
        except Exception as e:
            print(f"خطأ في تحميل الأنشطة: {e}")
    
    def closeEvent(self, event):
        """إيقاف المؤقت عند الإغلاق"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        event.accept()

    def get_dashboard_style(self):
        """أنماط خاصة للوحة التحكم"""
        return """
            #dashboardCard {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8fafc);
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 15px;
            }
            #dashboardCard:hover {
                border-color: #3b82f6;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            }
            #scrollArea {
                border: none;
                background-color: transparent;
            }
        """
