from datetime import datetime
import sqlite3
from database.database import DatabaseManager

class Medication:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def add_medication(self, name, description=None):
        """إضافة دواء جديد"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO medications (name, description)
                VALUES (?, ?)
            ''', (name.strip(), description))
            
            medication_id = cursor.lastrowid
            conn.commit()
            return medication_id
        except sqlite3.IntegrityError:
            # إذا كان الدواء موجود، نزيد عداد الاستخدام
            cursor.execute('''
                UPDATE medications 
                SET usage_count = usage_count + 1, updated_at = CURRENT_TIMESTAMP
                WHERE name = ?
            ''', (name.strip(),))
            conn.commit()
            
            # الحصول على ID الدواء الموجود
            cursor.execute('SELECT id FROM medications WHERE name = ?', (name.strip(),))
            result = cursor.fetchone()
            return result['id'] if result else None
        finally:
            conn.close()
    
    def get_medication(self, medication_id):
        """الحصول على بيانات دواء"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM medications WHERE id = ?', (medication_id,))
        result = cursor.fetchone()
        conn.close()
        return dict(result) if result else None
    
    def update_medication(self, medication_id, **kwargs):
        """تحديث بيانات دواء"""
        if not kwargs:
            return False
        
        # إضافة تاريخ التحديث
        kwargs['updated_at'] = datetime.now().isoformat()
        
        # بناء استعلام التحديث
        set_clause = ', '.join([f"{key} = ?" for key in kwargs.keys()])
        values = list(kwargs.values()) + [medication_id]
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute(f'UPDATE medications SET {set_clause} WHERE id = ?', values)
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        return affected_rows > 0
    
    def delete_medication(self, medication_id):
        """حذف دواء"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM medications WHERE id = ?', (medication_id,))
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        return affected_rows > 0
    
    def search_medications(self, search_term="", limit=100):
        """البحث في الأدوية"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        if search_term:
            cursor.execute('''
                SELECT * FROM medications 
                WHERE name LIKE ? OR description LIKE ?
                ORDER BY usage_count DESC, name
                LIMIT ?
            ''', (f'%{search_term}%', f'%{search_term}%', limit))
        else:
            cursor.execute('''
                SELECT * FROM medications 
                ORDER BY usage_count DESC, name
                LIMIT ?
            ''', (limit,))
        
        results = cursor.fetchall()
        conn.close()
        return [dict(row) for row in results]
    
    def get_medication_suggestions(self, partial_name, limit=10):
        """الحصول على اقتراحات الأدوية للإكمال التلقائي"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT name FROM medications 
            WHERE name LIKE ?
            ORDER BY usage_count DESC, name
            LIMIT ?
        ''', (f'{partial_name}%', limit))
        
        results = cursor.fetchall()
        conn.close()
        return [row['name'] for row in results]
    
    def get_all_medication_names(self):
        """الحصول على جميع أسماء الأدوية للإكمال التلقائي"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT name FROM medications 
            ORDER BY usage_count DESC, name
        ''')
        
        results = cursor.fetchall()
        conn.close()
        return [row['name'] for row in results]
    
    def increment_usage(self, medication_name):
        """زيادة عداد استخدام الدواء"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE medications 
            SET usage_count = usage_count + 1, updated_at = CURRENT_TIMESTAMP
            WHERE name = ?
        ''', (medication_name.strip(),))
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        return affected_rows > 0
    
    def get_popular_medications(self, limit=20):
        """الحصول على الأدوية الأكثر استخداماً"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT * FROM medications 
            WHERE usage_count > 1
            ORDER BY usage_count DESC, name
            LIMIT ?
        ''', (limit,))
        
        results = cursor.fetchall()
        conn.close()
        return [dict(row) for row in results]
