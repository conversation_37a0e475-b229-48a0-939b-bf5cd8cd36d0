from datetime import datetime
import sqlite3
from database.database import DatabaseManager

class Patient:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def add_patient(self, file_number, full_name, phone=None, gender=None, age=None, address=None, photo_path=None):
        """إضافة مريض جديد"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO patients (file_number, full_name, phone, gender, age, address, photo_path)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (file_number, full_name, phone, gender, age, address, photo_path))
            
            patient_id = cursor.lastrowid
            conn.commit()
            return patient_id
        except sqlite3.IntegrityError:
            raise Exception(f"رقم الملف {file_number} موجود مسبقاً")
        finally:
            conn.close()
    
    def get_patient(self, patient_id):
        """الحصول على بيانات مريض"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM patients WHERE id = ?', (patient_id,))
        result = cursor.fetchone()
        conn.close()
        return dict(result) if result else None
    
    def get_patient_by_file_number(self, file_number):
        """الحصول على مريض برقم الملف"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM patients WHERE file_number = ?', (file_number,))
        result = cursor.fetchone()
        conn.close()
        return dict(result) if result else None
    
    def update_patient(self, patient_id, **kwargs):
        """تحديث بيانات مريض"""
        if not kwargs:
            return False
        
        # إضافة تاريخ التحديث
        kwargs['updated_at'] = datetime.now().isoformat()
        
        # بناء استعلام التحديث
        set_clause = ', '.join([f"{key} = ?" for key in kwargs.keys()])
        values = list(kwargs.values()) + [patient_id]
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute(f'UPDATE patients SET {set_clause} WHERE id = ?', values)
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        return affected_rows > 0
    
    def delete_patient(self, patient_id):
        """حذف مريض"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM patients WHERE id = ?', (patient_id,))
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        return affected_rows > 0
    
    def search_patients(self, search_term="", limit=100, offset=0):
        """البحث في المرضى"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        if search_term:
            cursor.execute('''
                SELECT * FROM patients 
                WHERE full_name LIKE ? OR file_number LIKE ? OR phone LIKE ?
                ORDER BY full_name
                LIMIT ? OFFSET ?
            ''', (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%', limit, offset))
        else:
            cursor.execute('''
                SELECT * FROM patients 
                ORDER BY full_name
                LIMIT ? OFFSET ?
            ''', (limit, offset))
        
        results = cursor.fetchall()
        conn.close()
        return [dict(row) for row in results]
    
    def get_patients_count(self, search_term=""):
        """عدد المرضى"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        if search_term:
            cursor.execute('''
                SELECT COUNT(*) as count FROM patients 
                WHERE full_name LIKE ? OR file_number LIKE ? OR phone LIKE ?
            ''', (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))
        else:
            cursor.execute('SELECT COUNT(*) as count FROM patients')
        
        result = cursor.fetchone()
        conn.close()
        return result['count'] if result else 0
    
    def get_next_file_number(self):
        """الحصول على رقم الملف التالي"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT MAX(CAST(file_number AS INTEGER)) as max_num FROM patients WHERE file_number GLOB "[0-9]*"')
        result = cursor.fetchone()
        conn.close()
        
        max_num = result['max_num'] if result and result['max_num'] else 0
        return str(max_num + 1)
