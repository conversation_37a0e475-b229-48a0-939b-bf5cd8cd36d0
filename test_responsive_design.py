#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للتصميم المرن - التأكد من أن جميع العناصر تتكيف مع أحجام الشاشة المختلفة
Test responsive design - Ensure all elements adapt to different screen sizes
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QMessageBox
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QFont

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النماذج والواجهات
from database.database import DatabaseManager
from models.user import User
from models.patient import Patient
from models.medication import Medication
from models.visit import Visit
from ui.login_window import LoginWindow
from ui.main_window import MainWindow

class ResponsiveTestWindow(QWidget):
    """نافذة اختبار التصميم المرن"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار التصميم المرن - جميع أحجام الشاشة")
        self.setMinimumSize(800, 600)
        self.resize(1000, 700)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title = QLabel("🧪 اختبار التصميم المرن - إدارة العيادة الطبية")
        title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                padding: 15px;
                color: white;
            }
        """)
        layout.addWidget(title)
        
        # معلومات الاختبار
        info_label = QLabel("""
📋 هذا الاختبار يفحص:
• مرونة جميع الشاشات مع أحجام الشاشة المختلفة
• عدم وجود أحجام ثابتة تسبب مشاكل
• ظهور جميع الأزرار والعناصر بشكل صحيح
• التكيف مع الشاشات الصغيرة والكبيرة
        """)
        info_label.setFont(QFont("Arial", 11))
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 15px;
                color: #2c3e50;
            }
        """)
        layout.addWidget(info_label)
        
        # أزرار الاختبار
        buttons_layout = QHBoxLayout()
        
        # اختبار شاشة صغيرة
        small_btn = QPushButton("📱 شاشة صغيرة (800x600)")
        small_btn.clicked.connect(self.test_small_screen)
        small_btn.setMinimumHeight(50)
        buttons_layout.addWidget(small_btn)
        
        # اختبار شاشة متوسطة
        medium_btn = QPushButton("💻 شاشة متوسطة (1366x768)")
        medium_btn.clicked.connect(self.test_medium_screen)
        medium_btn.setMinimumHeight(50)
        buttons_layout.addWidget(medium_btn)
        
        # اختبار شاشة كبيرة
        large_btn = QPushButton("🖥️ شاشة كبيرة (1920x1080)")
        large_btn.clicked.connect(self.test_large_screen)
        large_btn.setMinimumHeight(50)
        buttons_layout.addWidget(large_btn)
        
        layout.addLayout(buttons_layout)
        
        # أزرار اختبار الشاشات المحددة
        test_buttons_layout = QHBoxLayout()
        
        login_btn = QPushButton("🔐 اختبار شاشة تسجيل الدخول")
        login_btn.clicked.connect(self.test_login_window)
        login_btn.setMinimumHeight(40)
        test_buttons_layout.addWidget(login_btn)
        
        main_btn = QPushButton("🏥 اختبار النافذة الرئيسية")
        main_btn.clicked.connect(self.test_main_window)
        main_btn.setMinimumHeight(40)
        test_buttons_layout.addWidget(main_btn)
        
        layout.addLayout(test_buttons_layout)
        
        # نتائج الاختبار
        self.results_label = QLabel("🔄 انقر على أي زر لبدء الاختبار...")
        self.results_label.setFont(QFont("Arial", 12))
        self.results_label.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                border: 1px solid #3498db;
                border-radius: 8px;
                padding: 15px;
                color: #2c3e50;
            }
        """)
        layout.addWidget(self.results_label)
        
        # تطبيق الأنماط العامة
        self.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 15px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
    
    def test_small_screen(self):
        """اختبار شاشة صغيرة"""
        self.resize(800, 600)
        self.update_results("✅ تم تطبيق حجم الشاشة الصغيرة (800x600)")
    
    def test_medium_screen(self):
        """اختبار شاشة متوسطة"""
        self.resize(1366, 768)
        self.update_results("✅ تم تطبيق حجم الشاشة المتوسطة (1366x768)")
    
    def test_large_screen(self):
        """اختبار شاشة كبيرة"""
        self.resize(1920, 1080)
        self.update_results("✅ تم تطبيق حجم الشاشة الكبيرة (1920x1080)")
    
    def test_login_window(self):
        """اختبار شاشة تسجيل الدخول"""
        try:
            # إنشاء قاعدة البيانات
            db_manager = DatabaseManager()
            
            # إنشاء نافذة تسجيل الدخول
            login_window = LoginWindow(db_manager)
            login_window.show()
            
            self.update_results("✅ تم فتح شاشة تسجيل الدخول بنجاح - اختبر التكبير والتصغير")
            
        except Exception as e:
            self.update_results(f"❌ خطأ في اختبار شاشة تسجيل الدخول: {str(e)}")
    
    def test_main_window(self):
        """اختبار النافذة الرئيسية"""
        try:
            # إنشاء قاعدة البيانات
            db_manager = DatabaseManager()
            
            # بيانات مستخدم تجريبي
            user_data = {
                'id': 1,
                'username': 'admin',
                'full_name': 'المدير العام',
                'role': 'admin'
            }
            
            # إنشاء النافذة الرئيسية
            main_window = MainWindow(db_manager, user_data)
            main_window.show()
            
            self.update_results("✅ تم فتح النافذة الرئيسية بنجاح - اختبر جميع الشاشات والتكبير والتصغير")
            
        except Exception as e:
            self.update_results(f"❌ خطأ في اختبار النافذة الرئيسية: {str(e)}")
    
    def update_results(self, message):
        """تحديث نتائج الاختبار"""
        self.results_label.setText(f"📊 نتيجة الاختبار: {message}")

def main():
    """تشغيل اختبار التصميم المرن"""
    app = QApplication(sys.argv)
    
    # ضبط خط التطبيق
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة
    test_window = ResponsiveTestWindow()
    test_window.show()
    
    # رسالة ترحيب
    QMessageBox.information(
        test_window,
        "اختبار التصميم المرن",
        """🎯 مرحباً بك في اختبار التصميم المرن!

📋 الهدف من هذا الاختبار:
• التأكد من أن جميع العناصر تظهر بشكل صحيح
• اختبار التكيف مع أحجام الشاشة المختلفة
• فحص عدم وجود مشاكل في الأزرار أو العناصر

⚡ التعليمات:
1. اختبر الأحجام المختلفة للشاشة
2. افتح الشاشات المختلفة واختبر التكبير والتصغير
3. تأكد من ظهور جميع الأزرار والعناصر
4. اختبر القوائم والجداول

✅ جميع التحديثات تم تطبيقها لجعل التصميم مرناً ومتكيفاً!"""
    )
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
