from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QMessageBox, QDialog, QDateEdit, QTextEdit,
                            QDoubleSpinBox, QHeaderView, QFrame, QFileDialog,
                            QListWidget, QListWidgetItem, QSplitter, QGroupBox, QFormLayout,
                            QTabWidget, QGridLayout, QScrollArea)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
import os
from datetime import datetime

class VisitDialog(QDialog):
    """نافذة إضافة/تعديل زيارة"""
    
    def __init__(self, visit_model, patient_data, visit_data=None, parent=None):
        super().__init__(parent)
        self.visit_model = visit_model
        self.patient_data = patient_data
        self.visit_data = visit_data
        self.is_edit_mode = visit_data is not None
        self.attached_images = []

        # جعل النافذة بحجم الشاشة الكاملة
        self.setWindowState(Qt.WindowState.WindowMaximized)

        self.init_ui()

        if self.is_edit_mode:
            self.load_visit_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل زيارة" if self.is_edit_mode else "إضافة زيارة جديدة"
        self.setWindowTitle(f"{title} - {self.patient_data['full_name']}")
        self.setMinimumSize(800, 700)
        self.resize(800, 700)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # معلومات المريض
        patient_frame = QFrame()
        patient_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f4fd;
                border-radius: 8px;
                border: 2px solid #3498db;
                padding: 10px;
            }
        """)
        patient_layout = QHBoxLayout(patient_frame)
        
        patient_info = QLabel(f"👤 المريض: {self.patient_data['full_name']} | 📁 رقم الملف: {self.patient_data['file_number']}")
        patient_info.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        patient_info.setStyleSheet("color: #2c3e50; border: none; padding: 5px;")
        patient_layout.addWidget(patient_info)
        
        layout.addWidget(patient_frame)
        
        # تخطيط رئيسي بسيط مع تبويبات
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                padding: 15px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 12px 24px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
        """)

        # التبويب الأول: بيانات الزيارة
        visit_tab = QWidget()
        visit_layout = QVBoxLayout(visit_tab)
        
        # مجموعة بيانات الزيارة
        visit_group = QGroupBox("📋 بيانات الزيارة")
        visit_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        visit_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
                background-color: #f8f9fa;
            }
        """)

        # تخطيط شبكي للحقول
        visit_grid = QGridLayout(visit_group)
        visit_grid.setSpacing(15)
        visit_grid.setContentsMargins(20, 25, 20, 20)

        # الصف الأول: تاريخ الزيارة والوزن
        visit_date_label = QLabel("📅 تاريخ الزيارة:")
        visit_date_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        self.visit_date = QDateEdit()
        self.visit_date.setDate(QDate.currentDate())
        self.visit_date.setCalendarPopup(True)
        self.visit_date.setMinimumHeight(40)
        self.visit_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                background-color: white;
            }
            QDateEdit:focus {
                border-color: #3498db;
            }
        """)
        visit_grid.addWidget(visit_date_label, 0, 0)
        visit_grid.addWidget(self.visit_date, 0, 1)

        weight_label = QLabel("⚖️ الوزن:")
        weight_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        self.weight_input = QDoubleSpinBox()
        self.weight_input.setRange(0, 500)
        self.weight_input.setSuffix(" كجم")
        self.weight_input.setDecimals(1)
        self.weight_input.setMinimumHeight(40)
        self.weight_input.setStyleSheet("""
            QDoubleSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                background-color: white;
            }
            QDoubleSpinBox:focus {
                border-color: #3498db;
            }
        """)
        visit_grid.addWidget(weight_label, 0, 2)
        visit_grid.addWidget(self.weight_input, 0, 3)

        # الصف الثاني: السكر والضغط
        sugar_label = QLabel("🩸 السكر:")
        sugar_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        self.blood_sugar_input = QDoubleSpinBox()
        self.blood_sugar_input.setRange(0, 1000)
        self.blood_sugar_input.setSuffix(" mg/dL")
        self.blood_sugar_input.setDecimals(0)
        self.blood_sugar_input.setMinimumHeight(40)
        self.blood_sugar_input.setStyleSheet("""
            QDoubleSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                background-color: white;
            }
            QDoubleSpinBox:focus {
                border-color: #3498db;
            }
        """)
        visit_grid.addWidget(sugar_label, 1, 0)
        visit_grid.addWidget(self.blood_sugar_input, 1, 1)

        pressure_label = QLabel("💓 ضغط الدم:")
        pressure_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        self.blood_pressure_input = QLineEdit()
        self.blood_pressure_input.setPlaceholderText("مثال: 120/80")
        self.blood_pressure_input.setMinimumHeight(40)
        self.blood_pressure_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        visit_grid.addWidget(pressure_label, 1, 2)
        visit_grid.addWidget(self.blood_pressure_input, 1, 3)

        visit_layout.addWidget(visit_group)
        
        # مجموعة الملاحظات والعلاج
        notes_group = QGroupBox("📝 الملاحظات والعلاج")
        notes_group.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        notes_layout = QVBoxLayout(notes_group)
        
        # الملاحظات
        notes_label = QLabel("📋 ملاحظات الزيارة:")
        notes_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        notes_layout.addWidget(notes_label)
        
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setMinimumHeight(60)
        self.notes_input.setPlaceholderText("أدخل ملاحظات الزيارة...")
        notes_layout.addWidget(self.notes_input)

        # التشخيص
        diagnosis_label = QLabel("🔍 التشخيص:")
        diagnosis_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        notes_layout.addWidget(diagnosis_label)

        self.diagnosis_input = QTextEdit()
        self.diagnosis_input.setMaximumHeight(80)
        self.diagnosis_input.setMinimumHeight(60)
        self.diagnosis_input.setPlaceholderText("أدخل التشخيص...")
        notes_layout.addWidget(self.diagnosis_input)

        # وصف العلاج
        treatment_label = QLabel("💊 وصف العلاج:")
        treatment_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        notes_layout.addWidget(treatment_label)

        # زر فتح محرر الوصفة المتقدم
        treatment_buttons_layout = QHBoxLayout()

        self.treatment_input = QTextEdit()
        self.treatment_input.setMaximumHeight(120)
        self.treatment_input.setMinimumHeight(100)
        self.treatment_input.setPlaceholderText("أدخل وصف العلاج أو استخدم المحرر المتقدم...")

        advanced_editor_button = QPushButton("📝 محرر متقدم")
        advanced_editor_button.setMinimumHeight(35)
        advanced_editor_button.setMinimumWidth(120)
        advanced_editor_button.clicked.connect(self.open_advanced_prescription_editor)

        treatment_container = QWidget()
        treatment_container_layout = QVBoxLayout(treatment_container)
        treatment_container_layout.setContentsMargins(0, 0, 0, 0)
        treatment_container_layout.addWidget(self.treatment_input)

        treatment_button_layout = QHBoxLayout()

        # زر التنسيق التلقائي
        format_button = QPushButton("✨ تنسيق تلقائي")
        format_button.setMinimumHeight(35)
        format_button.setMinimumWidth(120)
        format_button.clicked.connect(self.format_prescription)
        treatment_button_layout.addWidget(format_button)

        treatment_button_layout.addStretch()
        treatment_button_layout.addWidget(advanced_editor_button)
        treatment_container_layout.addLayout(treatment_button_layout)

        notes_layout.addWidget(treatment_container)
        visit_layout.addWidget(notes_group)

        # إضافة التبويب الأول
        tab_widget.addTab(visit_tab, "📋 بيانات الزيارة")

        # التبويب الثاني: الصور والمرفقات
        files_tab = QWidget()
        files_layout = QVBoxLayout(files_tab)

        # مجموعة الصور المرفقة
        images_group = QGroupBox("🖼️ الصور والمرفقات")
        images_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        images_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e67e22;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #fdf6e3;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
                background-color: #fdf6e3;
            }
        """)
        images_layout = QVBoxLayout(images_group)
        images_layout.setContentsMargins(20, 25, 20, 20)

        # أزرار إدارة الصور
        images_buttons_layout = QHBoxLayout()
        images_buttons_layout.setSpacing(15)

        add_image_btn = QPushButton("📎 إضافة صورة/ملف")
        add_image_btn.setMinimumHeight(45)
        add_image_btn.setMinimumWidth(150)
        add_image_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_image_btn.clicked.connect(self.add_image)
        images_buttons_layout.addWidget(add_image_btn)

        view_image_btn = QPushButton("👁️ فتح الملف")
        view_image_btn.setMinimumHeight(45)
        view_image_btn.setMinimumWidth(150)
        view_image_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        view_image_btn.clicked.connect(self.view_image)
        images_buttons_layout.addWidget(view_image_btn)

        remove_image_btn = QPushButton("🗑️ حذف الملف")
        remove_image_btn.setMinimumHeight(45)
        remove_image_btn.setMinimumWidth(150)
        remove_image_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        remove_image_btn.clicked.connect(self.remove_image)
        images_buttons_layout.addWidget(remove_image_btn)

        images_buttons_layout.addStretch()
        images_layout.addLayout(images_buttons_layout)

        # قائمة الصور والملفات
        self.images_list = QListWidget()
        self.images_list.setMinimumHeight(400)
        self.images_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                padding: 10px;
                font-size: 14px;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #ecf0f1;
                border-radius: 4px;
                margin: 2px;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #ecf0f1;
            }
        """)
        images_layout.addWidget(self.images_list)

        files_layout.addWidget(images_group)

        # إضافة التبويب الثاني
        tab_widget.addTab(files_tab, "📎 الصور والمرفقات")

        layout.addWidget(tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        save_button = QPushButton("💾 حفظ الزيارة")
        save_button.setMinimumHeight(40)
        save_button.setMinimumWidth(150)
        save_button.clicked.connect(self.save_visit)
        buttons_layout.addWidget(save_button)

        # زر حفظ الوصفة فقط
        save_prescription_button = QPushButton("📝 حفظ الوصفة")
        save_prescription_button.setMinimumHeight(40)
        save_prescription_button.setMinimumWidth(150)
        save_prescription_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_prescription_button.clicked.connect(self.save_prescription_only)
        buttons_layout.addWidget(save_prescription_button)

        # زر طباعة الوصفة
        print_prescription_button = QPushButton("🖨️ طباعة الوصفة")
        print_prescription_button.setMinimumHeight(40)
        print_prescription_button.setMinimumWidth(150)
        print_prescription_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        print_prescription_button.clicked.connect(self.print_prescription_direct)
        buttons_layout.addWidget(print_prescription_button)

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(40)
        cancel_button.setFixedWidth(120)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)

        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            QLineEdit, QDoubleSpinBox, QDateEdit, QTextEdit {
                padding: 8px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus, QTextEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QListWidget {
                border: 2px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #e9ecef;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
    
    def add_image(self):
        """إضافة صورة"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self, "اختر صورة", "", 
            "Image files (*.png *.jpg *.jpeg *.bmp *.gif);;All files (*.*)"
        )
        
        if file_path:
            file_name = os.path.basename(file_path)
            self.attached_images.append(file_path)
            
            item = QListWidgetItem(f"📎 {file_name}")
            item.setData(Qt.ItemDataRole.UserRole, file_path)
            self.images_list.addItem(item)
    
    def view_image(self):
        """فتح الصورة أو الملف"""
        try:
            current_item = self.images_list.currentItem()
            if current_item:
                file_path = current_item.data(Qt.ItemDataRole.UserRole)

                if os.path.exists(file_path):
                    # تحديد نوع الملف
                    file_ext = os.path.splitext(file_path)[1].lower()

                    if file_ext in ['.png', '.jpg', '.jpeg', '.bmp', '.gif']:
                        # فتح الصورة في نافذة منفصلة
                        self.open_image_viewer(file_path, os.path.basename(file_path))
                    else:
                        # فتح الملف بالبرنامج الافتراضي
                        import subprocess
                        import platform

                        if platform.system() == 'Windows':
                            os.startfile(file_path)
                        elif platform.system() == 'Darwin':  # macOS
                            subprocess.call(['open', file_path])
                        else:  # Linux
                            subprocess.call(['xdg-open', file_path])
                else:
                    QMessageBox.warning(self, "خطأ", "الملف غير موجود")
            else:
                QMessageBox.warning(self, "تنبيه", "يرجى اختيار ملف من القائمة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الملف: {str(e)}")

    def open_image_viewer(self, image_path, image_name):
        """فتح عارض الصورة"""
        try:
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton
            from PyQt6.QtGui import QPixmap
            from PyQt6.QtCore import Qt

            # إنشاء نافذة عرض الصورة
            image_dialog = QDialog(self)
            image_dialog.setWindowTitle(f"عرض الصورة - {image_name}")
            image_dialog.setModal(True)
            image_dialog.resize(800, 600)

            layout = QVBoxLayout(image_dialog)

            # عرض الصورة
            image_label = QLabel()
            pixmap = QPixmap(image_path)

            # تصغير الصورة للنافذة
            scaled_pixmap = pixmap.scaled(
                750, 550,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            image_label.setPixmap(scaled_pixmap)
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            image_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #3498db;
                    border-radius: 8px;
                    background-color: white;
                    padding: 10px;
                }
            """)
            layout.addWidget(image_label)

            # زر إغلاق
            close_button = QPushButton("إغلاق")
            close_button.setMinimumHeight(40)
            close_button.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 14px;
                    padding: 10px 20px;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """)
            close_button.clicked.connect(image_dialog.close)
            layout.addWidget(close_button)

            image_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عرض الصورة: {str(e)}")

    def remove_image(self):
        """حذف صورة"""
        current_item = self.images_list.currentItem()
        if current_item:
            file_path = current_item.data(Qt.ItemDataRole.UserRole)
            if file_path in self.attached_images:
                self.attached_images.remove(file_path)

            row = self.images_list.row(current_item)
            self.images_list.takeItem(row)
    
    def load_visit_data(self):
        """تحميل بيانات الزيارة للتعديل"""
        if self.visit_data:
            # تحميل التاريخ
            visit_date = datetime.strptime(self.visit_data['visit_date'], '%Y-%m-%d').date()
            self.visit_date.setDate(QDate(visit_date))
            
            # تحميل البيانات الطبية
            if self.visit_data['weight']:
                self.weight_input.setValue(self.visit_data['weight'])
            
            if self.visit_data['blood_sugar']:
                self.blood_sugar_input.setValue(self.visit_data['blood_sugar'])
            
            if self.visit_data['blood_pressure']:
                self.blood_pressure_input.setText(self.visit_data['blood_pressure'])

            if self.visit_data.get('diagnosis'):
                self.diagnosis_input.setPlainText(self.visit_data['diagnosis'])

            if self.visit_data['notes']:
                self.notes_input.setPlainText(self.visit_data['notes'])

            if self.visit_data['treatment_description']:
                self.treatment_input.setPlainText(self.visit_data['treatment_description'])
            
            # تحميل الصور المرفقة
            images = self.visit_model.get_visit_images(self.visit_data['id'])
            for image in images:
                item = QListWidgetItem(f"📎 {os.path.basename(image['image_path'])}")
                item.setData(Qt.ItemDataRole.UserRole, image['image_path'])
                self.images_list.addItem(item)
                self.attached_images.append(image['image_path'])
    
    def save_visit(self):
        """حفظ الزيارة"""
        # جمع البيانات
        visit_date = self.visit_date.date().toString('yyyy-MM-dd')
        weight = self.weight_input.value() if self.weight_input.value() > 0 else None
        blood_sugar = self.blood_sugar_input.value() if self.blood_sugar_input.value() > 0 else None
        blood_pressure = self.blood_pressure_input.text().strip() or None
        diagnosis = self.diagnosis_input.toPlainText().strip() or None
        notes = self.notes_input.toPlainText().strip() or None
        treatment = self.treatment_input.toPlainText().strip() or None
        
        try:
            if self.is_edit_mode:
                # تحديث الزيارة
                success = self.visit_model.update_visit(
                    self.visit_data['id'],
                    visit_date=visit_date,
                    weight=weight,
                    blood_sugar=blood_sugar,
                    blood_pressure=blood_pressure,
                    diagnosis=diagnosis,
                    notes=notes,
                    treatment_description=treatment
                )
                visit_id = self.visit_data['id']
            else:
                # إضافة زيارة جديدة
                visit_id = self.visit_model.add_visit(
                    self.patient_data['id'],
                    visit_date=visit_date,
                    weight=weight,
                    blood_sugar=blood_sugar,
                    blood_pressure=blood_pressure,
                    diagnosis=diagnosis,
                    notes=notes,
                    treatment_description=treatment
                )
                success = visit_id is not None
            
            if success:
                # حفظ الصور المرفقة
                for image_path in self.attached_images:
                    if os.path.exists(image_path):
                        self.visit_model.add_visit_image(
                            visit_id, image_path, 
                            image_type="medical_image",
                            description="صورة طبية"
                        )
                
                QMessageBox.information(self, "نجح", "تم حفظ الزيارة بنجاح")
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في حفظ الزيارة")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الزيارة:\n{str(e)}")

    def open_advanced_prescription_editor(self):
        """فتح محرر الوصفة المتقدم"""
        try:
            from ui.prescription_widget import PrescriptionWidget
            from models.medication import Medication

            # إنشاء نافذة المحرر المتقدم
            prescription_dialog = QDialog(self)
            prescription_dialog.setWindowTitle("محرر الوصفة المتقدم")
            prescription_dialog.setFixedSize(1000, 700)

            layout = QVBoxLayout(prescription_dialog)
            layout.setContentsMargins(0, 0, 0, 0)

            # إنشاء نموذج الأدوية
            medication_model = Medication(self.visit_model.db)

            # إنشاء ويدجت الوصفة مع النص الحالي
            visit_data = {'treatment_description': self.treatment_input.toPlainText()}
            prescription_widget = PrescriptionWidget(medication_model, visit_data)
            layout.addWidget(prescription_widget)

            # أزرار التحكم
            buttons_layout = QHBoxLayout()

            apply_button = QPushButton("✅ تطبيق")
            apply_button.setFixedHeight(40)
            apply_button.clicked.connect(lambda: self.apply_prescription(prescription_widget, prescription_dialog))
            buttons_layout.addWidget(apply_button)

            cancel_button = QPushButton("❌ إلغاء")
            cancel_button.setFixedHeight(40)
            cancel_button.clicked.connect(prescription_dialog.reject)
            buttons_layout.addWidget(cancel_button)

            buttons_layout.addStretch()
            layout.addLayout(buttons_layout)

            prescription_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح المحرر المتقدم:\n{str(e)}")

    def apply_prescription(self, prescription_widget, dialog):
        """تطبيق النص من المحرر المتقدم"""
        prescription_text = prescription_widget.prescription_text.toPlainText()
        self.treatment_input.setPlainText(prescription_text)
        dialog.accept()

    def format_prescription(self):
        """تنسيق الوصفة تلقائياً"""
        try:
            from utils.prescription_formatter import PrescriptionFormatter

            current_text = self.treatment_input.toPlainText()
            if not current_text.strip():
                QMessageBox.information(self, "تنبيه", "لا يوجد نص لتنسيقه")
                return

            formatter = PrescriptionFormatter()
            formatted_text = formatter.format_prescription(current_text)

            # عرض النتيجة للمستخدم
            if formatted_text != current_text:
                self.treatment_input.setPlainText(formatted_text)
                QMessageBox.information(self, "تم التنسيق", "تم تنسيق الوصفة بنجاح")
            else:
                QMessageBox.information(self, "لا حاجة للتنسيق", "الوصفة منسقة بالفعل")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تنسيق الوصفة: {str(e)}")

    def save_prescription_only(self):
        """حفظ الوصفة فقط بدون باقي بيانات الزيارة"""
        treatment = self.treatment_input.toPlainText().strip()

        if not treatment:
            QMessageBox.warning(self, "تحذير", "لا يوجد وصفة للحفظ")
            return

        try:
            if self.is_edit_mode and self.visit_data:
                # تحديث الوصفة فقط
                success = self.visit_model.update_visit(
                    self.visit_data['id'],
                    treatment_description=treatment
                )

                if success:
                    QMessageBox.information(self, "نجح", "تم حفظ الوصفة بنجاح")
                    # تحديث البيانات المحلية
                    self.visit_data['treatment_description'] = treatment
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في حفظ الوصفة")
            else:
                QMessageBox.warning(self, "تحذير", "يجب حفظ الزيارة أولاً قبل حفظ الوصفة منفصلة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الوصفة:\n{str(e)}")

    def print_prescription_direct(self):
        """طباعة الوصفة مباشرة"""
        treatment = self.treatment_input.toPlainText().strip()

        if not treatment:
            QMessageBox.warning(self, "تحذير", "لا يوجد وصفة للطباعة")
            return

        try:
            from utils.print_manager import PrescriptionPrintDialog

            # إنشاء بيانات وهمية للطباعة إذا لم تكن الزيارة محفوظة
            if self.is_edit_mode and self.visit_data:
                visit_data = self.visit_data.copy()
                visit_data['treatment_description'] = treatment
            else:
                # إنشاء بيانات مؤقتة للطباعة
                visit_data = {
                    'id': 'temp',
                    'visit_date': self.visit_date.date().toString('yyyy-MM-dd'),
                    'treatment_description': treatment,
                    'diagnosis': self.diagnosis_input.toPlainText().strip(),
                    'notes': self.notes_input.toPlainText().strip()
                }

            # الحصول على إعدادات العيادة
            clinic_settings = {
                'clinic_name': 'عيادة طبية',  # يمكن تحسينها لاحقاً
                'doctor_name': 'د. طبيب',
                'clinic_address': 'عنوان العيادة',
                'clinic_phone': 'رقم الهاتف'
            }

            # فتح نافذة الطباعة
            print_dialog = PrescriptionPrintDialog(
                self.patient_data, visit_data, clinic_settings, self
            )
            print_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الطباعة: {str(e)}")

class VisitsWidget(QWidget):
    """ويدجت إدارة زيارات المريض"""
    
    def __init__(self, visit_model, patient_data, parent=None):
        super().__init__(parent)
        self.visit_model = visit_model
        self.patient_data = patient_data
        self.current_visits = []
        self.init_ui()
        self.load_visits()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # معلومات المريض
        patient_frame = QFrame()
        patient_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f4fd;
                border-radius: 8px;
                border: 2px solid #3498db;
                padding: 15px;
            }
        """)
        patient_layout = QVBoxLayout(patient_frame)
        
        patient_title = QLabel(f"📋 زيارات المريض: {self.patient_data['full_name']}")
        patient_title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        patient_title.setStyleSheet("color: #2c3e50; border: none; padding: 0;")
        patient_layout.addWidget(patient_title)
        
        patient_info = QLabel(f"📁 رقم الملف: {self.patient_data['file_number']} | 📞 الهاتف: {self.patient_data.get('phone', 'غير محدد')}")
        patient_info.setStyleSheet("color: #7f8c8d; border: none; padding: 0; margin-top: 5px;")
        patient_layout.addWidget(patient_info)
        
        layout.addWidget(patient_frame)
        
        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 10px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        
        add_visit_btn = QPushButton("➕ إضافة زيارة جديدة")
        add_visit_btn.setFixedHeight(35)
        add_visit_btn.clicked.connect(self.add_visit)
        buttons_layout.addWidget(add_visit_btn)
        
        edit_visit_btn = QPushButton("✏️ تعديل زيارة")
        edit_visit_btn.setFixedHeight(35)
        edit_visit_btn.clicked.connect(self.edit_visit)
        buttons_layout.addWidget(edit_visit_btn)
        
        delete_visit_btn = QPushButton("🗑️ حذف زيارة")
        delete_visit_btn.setFixedHeight(35)
        delete_visit_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_visit_btn.clicked.connect(self.delete_visit)
        buttons_layout.addWidget(delete_visit_btn)

        # زر طباعة الوصفة
        print_prescription_btn = QPushButton("🖨️ طباعة وصفة")
        print_prescription_btn.setFixedHeight(35)
        print_prescription_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        print_prescription_btn.clicked.connect(self.print_prescription)
        buttons_layout.addWidget(print_prescription_btn)

        buttons_layout.addStretch()

        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFixedHeight(35)
        refresh_btn.clicked.connect(self.load_visits)
        buttons_layout.addWidget(refresh_btn)
        
        layout.addWidget(buttons_frame)
        
        # جدول الزيارات
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(15, 15, 15, 15)
        
        table_title = QLabel("📅 سجل الزيارات")
        table_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        table_title.setStyleSheet("color: #2c3e50; border: none; padding: 0; margin-bottom: 10px;")
        table_layout.addWidget(table_title)
        
        self.visits_table = QTableWidget()
        self.visits_table.setColumnCount(7)
        self.visits_table.setHorizontalHeaderLabels([
            "التاريخ", "الوزن", "السكر", "الضغط", "التشخيص", "الملاحظات", "العلاج"
        ])
        
        # تعديل عرض الأعمدة
        header = self.visits_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Stretch)
        
        self.visits_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.visits_table.setAlternatingRowColors(True)
        self.visits_table.doubleClicked.connect(self.edit_visit)
        self.visits_table.setMinimumHeight(400)
        
        table_layout.addWidget(self.visits_table)
        layout.addWidget(table_frame)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: none;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)
    
    def load_visits(self):
        """تحميل زيارات المريض"""
        try:
            self.current_visits = self.visit_model.get_patient_visits(self.patient_data['id'])
            self.update_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الزيارات: {str(e)}")
    
    def update_table(self):
        """تحديث جدول الزيارات"""
        self.visits_table.setRowCount(len(self.current_visits))
        
        for row, visit in enumerate(self.current_visits):
            # التاريخ
            visit_date = datetime.strptime(visit['visit_date'], '%Y-%m-%d').strftime('%Y/%m/%d')
            self.visits_table.setItem(row, 0, QTableWidgetItem(visit_date))
            
            # الوزن
            weight = f"{visit['weight']} كجم" if visit['weight'] else ""
            self.visits_table.setItem(row, 1, QTableWidgetItem(weight))
            
            # السكر
            sugar = f"{visit['blood_sugar']} mg/dL" if visit['blood_sugar'] else ""
            self.visits_table.setItem(row, 2, QTableWidgetItem(sugar))
            
            # الضغط
            pressure = visit['blood_pressure'] or ""
            self.visits_table.setItem(row, 3, QTableWidgetItem(pressure))

            # التشخيص
            diagnosis = visit.get('diagnosis', '') or ""
            if len(diagnosis) > 30:
                diagnosis = diagnosis[:30] + "..."
            self.visits_table.setItem(row, 4, QTableWidgetItem(diagnosis))

            # الملاحظات
            notes = visit['notes'] or ""
            if len(notes) > 30:
                notes = notes[:30] + "..."
            self.visits_table.setItem(row, 5, QTableWidgetItem(notes))

            # العلاج
            treatment = visit['treatment_description'] or ""
            if len(treatment) > 30:
                treatment = treatment[:30] + "..."
            self.visits_table.setItem(row, 6, QTableWidgetItem(treatment))
    
    def add_visit(self):
        """إضافة زيارة جديدة"""
        dialog = VisitDialog(self.visit_model, self.patient_data, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_visits()
    
    def edit_visit(self):
        """تعديل زيارة"""
        current_row = self.visits_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_visits):
            visit_data = self.current_visits[current_row]
            dialog = VisitDialog(self.visit_model, self.patient_data, visit_data, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_visits()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار زيارة للتعديل")
    
    def delete_visit(self):
        """حذف زيارة"""
        current_row = self.visits_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_visits):
            visit_data = self.current_visits[current_row]
            visit_date = datetime.strptime(visit_data['visit_date'], '%Y-%m-%d').strftime('%Y/%m/%d')
            
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف زيارة تاريخ {visit_date}؟\n\nسيتم حذف جميع البيانات والصور المرفقة!",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    success = self.visit_model.delete_visit(visit_data['id'])
                    if success:
                        QMessageBox.information(self, "نجح", "تم حذف الزيارة بنجاح")
                        self.load_visits()
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف الزيارة")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار زيارة للحذف")

    def print_prescription(self):
        """طباعة وصفة الزيارة"""
        current_row = self.visits_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_visits):
            visit_data = self.current_visits[current_row]

            # التحقق من وجود وصف علاج
            if not visit_data.get('treatment_description'):
                QMessageBox.warning(self, "تحذير", "لا يوجد وصف علاج لهذه الزيارة")
                return

            try:
                from utils.print_manager import PrescriptionPrintDialog

                # الحصول على إعدادات العيادة
                clinic_settings = {
                    'clinic_name': self.visit_model.db.get_setting('clinic_name') or 'عيادة طبية',
                    'doctor_name': self.visit_model.db.get_setting('doctor_name') or 'د. طبيب',
                    'clinic_address': self.visit_model.db.get_setting('clinic_address') or 'عنوان العيادة',
                    'clinic_phone': self.visit_model.db.get_setting('clinic_phone') or 'رقم الهاتف'
                }

                # فتح نافذة الطباعة
                print_dialog = PrescriptionPrintDialog(
                    self.patient_data, visit_data, clinic_settings, self
                )
                print_dialog.exec()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الطباعة: {str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار زيارة لطباعة الوصفة")
