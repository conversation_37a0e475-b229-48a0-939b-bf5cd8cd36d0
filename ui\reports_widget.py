from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBox<PERSON>ayout, QLabel,
                            QFrame, QGridLayout, QPushButton, QDateEdit,
                            QComboBox, QTableWidget, QTableWidgetItem,
                            QTabWidget, QTextEdit, QMessageBox,
                            QGroupBox, QProgressBar)
from PyQt6.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt6.QtGui import QFont
from datetime import datetime, timedelta
import sqlite3

class ReportGeneratorThread(QThread):
    """خيط منفصل لإنشاء التقارير"""

    report_ready = pyqtSignal(dict)
    progress_updated = pyqtSignal(int)
    error_occurred = pyqtSignal(str)

    def __init__(self, db_manager, report_type, start_date, end_date, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.report_type = report_type
        self.start_date = start_date
        self.end_date = end_date

    def run(self):
        """تشغيل إنشاء التقرير"""
        try:
            if self.report_type == "visits":
                report_data = self.generate_visits_report()
            elif self.report_type == "medications":
                report_data = self.generate_medications_report()
            elif self.report_type == "diagnoses":
                report_data = self.generate_diagnoses_report()
            elif self.report_type == "patients":
                report_data = self.generate_patients_report()
            else:
                raise ValueError(f"نوع تقرير غير مدعوم: {self.report_type}")

            self.report_ready.emit(report_data)

        except Exception as e:
            self.error_occurred.emit(str(e))

    def generate_visits_report(self):
        """إنشاء تقرير الزيارات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        self.progress_updated.emit(20)

        # إحصائيات عامة
        cursor.execute("""
            SELECT COUNT(*) FROM visits
            WHERE visit_date BETWEEN ? AND ?
        """, (self.start_date, self.end_date))
        total_visits = cursor.fetchone()[0]

        self.progress_updated.emit(40)

        # الزيارات حسب التاريخ
        cursor.execute("""
            SELECT visit_date, COUNT(*) as count
            FROM visits
            WHERE visit_date BETWEEN ? AND ?
            GROUP BY visit_date
            ORDER BY visit_date
        """, (self.start_date, self.end_date))
        visits_by_date = cursor.fetchall()

        self.progress_updated.emit(60)

        # تفاصيل الزيارات
        cursor.execute("""
            SELECT v.visit_date, p.full_name, p.file_number, v.diagnosis, v.treatment_description
            FROM visits v
            JOIN patients p ON v.patient_id = p.id
            WHERE v.visit_date BETWEEN ? AND ?
            ORDER BY v.visit_date DESC
        """, (self.start_date, self.end_date))
        visit_details = cursor.fetchall()

        self.progress_updated.emit(80)

        # أكثر التشخيصات شيوعاً
        cursor.execute("""
            SELECT diagnosis, COUNT(*) as count
            FROM visits
            WHERE visit_date BETWEEN ? AND ? AND diagnosis IS NOT NULL AND diagnosis != ''
            GROUP BY diagnosis
            ORDER BY count DESC
            LIMIT 10
        """, (self.start_date, self.end_date))
        top_diagnoses = cursor.fetchall()

        self.progress_updated.emit(100)

        conn.close()

        return {
            'type': 'visits',
            'period': f"{self.start_date} إلى {self.end_date}",
            'total_visits': total_visits,
            'visits_by_date': visits_by_date,
            'visit_details': visit_details,
            'top_diagnoses': top_diagnoses
        }

    def generate_medications_report(self):
        """إنشاء تقرير الأدوية"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        self.progress_updated.emit(30)

        # الأدوية الأكثر استخداماً
        cursor.execute("""
            SELECT name, usage_count, description
            FROM medications
            ORDER BY usage_count DESC
            LIMIT 20
        """)
        top_medications = cursor.fetchall()

        self.progress_updated.emit(60)

        # إحصائيات عامة
        cursor.execute("SELECT COUNT(*) FROM medications")
        total_medications = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(usage_count) FROM medications")
        total_usage = cursor.fetchone()[0] or 0

        self.progress_updated.emit(100)

        conn.close()

        return {
            'type': 'medications',
            'total_medications': total_medications,
            'total_usage': total_usage,
            'top_medications': top_medications
        }

    def generate_diagnoses_report(self):
        """إنشاء تقرير التشخيصات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        self.progress_updated.emit(30)

        # التشخيصات الأكثر شيوعاً
        cursor.execute("""
            SELECT name, usage_count, description
            FROM diagnoses
            ORDER BY usage_count DESC
            LIMIT 20
        """)
        top_diagnoses = cursor.fetchall()

        self.progress_updated.emit(60)

        # إحصائيات عامة
        cursor.execute("SELECT COUNT(*) FROM diagnoses")
        total_diagnoses = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(usage_count) FROM diagnoses")
        total_usage = cursor.fetchone()[0] or 0

        self.progress_updated.emit(100)

        conn.close()

        return {
            'type': 'diagnoses',
            'total_diagnoses': total_diagnoses,
            'total_usage': total_usage,
            'top_diagnoses': top_diagnoses
        }

    def generate_patients_report(self):
        """إنشاء تقرير المرضى"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        self.progress_updated.emit(25)

        # إحصائيات عامة
        cursor.execute("SELECT COUNT(*) FROM patients")
        total_patients = cursor.fetchone()[0]

        self.progress_updated.emit(50)

        # المرضى حسب الجنس
        cursor.execute("""
            SELECT gender, COUNT(*) as count
            FROM patients
            GROUP BY gender
        """)
        patients_by_gender = cursor.fetchall()

        self.progress_updated.emit(75)

        # المرضى الأكثر زيارة
        cursor.execute("""
            SELECT p.full_name, p.file_number, COUNT(v.id) as visit_count
            FROM patients p
            LEFT JOIN visits v ON p.id = v.patient_id
            WHERE v.visit_date BETWEEN ? AND ?
            GROUP BY p.id
            ORDER BY visit_count DESC
            LIMIT 10
        """, (self.start_date, self.end_date))
        most_visited_patients = cursor.fetchall()

        self.progress_updated.emit(100)

        conn.close()

        return {
            'type': 'patients',
            'total_patients': total_patients,
            'patients_by_gender': patients_by_gender,
            'most_visited_patients': most_visited_patients
        }

class ReportsWidget(QWidget):
    """ويدجت التقارير المتقدمة"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_report_data = None
        self.init_ui()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # العنوان الرئيسي
        self.create_header(layout)

        # التبويبات
        tabs = QTabWidget()

        # تبويب إنشاء التقارير
        generate_tab = self.create_generate_tab()
        tabs.addTab(generate_tab, "📊 إنشاء التقارير")

        # تبويب عرض النتائج
        results_tab = self.create_results_tab()
        tabs.addTab(results_tab, "📋 النتائج")

        layout.addWidget(tabs)

        # تطبيق الستايل
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QTabWidget::pane {
                border: 1px solid #e9ecef;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)

    def create_header(self, layout):
        """إنشاء العنوان الرئيسي"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                padding: 20px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)

        title_label = QLabel("📊 التقارير والإحصائيات المتقدمة")
        title_label.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white; border: none; padding: 0;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)

        subtitle_label = QLabel("إنشاء تقارير شاملة ومفصلة لجميع أنشطة العيادة")
        subtitle_label.setStyleSheet("color: rgba(255, 255, 255, 0.9); border: none; padding: 0; font-size: 14px;")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(subtitle_label)

        layout.addWidget(header_frame)

    def create_generate_tab(self):
        """إنشاء تبويب إنشاء التقارير"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # إعدادات التقرير
        settings_group = QGroupBox("⚙️ إعدادات التقرير")
        settings_layout = QGridLayout(settings_group)

        # نوع التقرير
        type_label = QLabel("نوع التقرير:")
        type_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        settings_layout.addWidget(type_label, 0, 0)

        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "تقرير الزيارات",
            "تقرير الأدوية الأكثر استخداماً",
            "تقرير التشخيصات الأكثر شيوعاً",
            "تقرير المرضى"
        ])
        self.report_type_combo.setMinimumHeight(35)
        settings_layout.addWidget(self.report_type_combo, 0, 1)

        # تاريخ البداية
        start_label = QLabel("من تاريخ:")
        start_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        settings_layout.addWidget(start_label, 1, 0)

        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setMinimumHeight(35)
        self.start_date.setCalendarPopup(True)
        settings_layout.addWidget(self.start_date, 1, 1)

        # تاريخ النهاية
        end_label = QLabel("إلى تاريخ:")
        end_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        settings_layout.addWidget(end_label, 2, 0)

        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setMinimumHeight(35)
        self.end_date.setCalendarPopup(True)
        settings_layout.addWidget(self.end_date, 2, 1)

        layout.addWidget(settings_group)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: #ecf0f1;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.generate_button = QPushButton("📊 إنشاء التقرير")
        self.generate_button.setMinimumHeight(40)
        self.generate_button.clicked.connect(self.generate_report)
        buttons_layout.addWidget(self.generate_button)

        buttons_layout.addStretch()

        # زر الطباعة
        print_button = QPushButton("🖨️ طباعة التقرير")
        print_button.setMinimumHeight(40)
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        print_button.clicked.connect(self.print_report)
        buttons_layout.addWidget(print_button)

        export_button = QPushButton("📄 تصدير PDF")
        export_button.setMinimumHeight(40)
        export_button.clicked.connect(self.export_to_pdf)
        buttons_layout.addWidget(export_button)

        layout.addLayout(buttons_layout)
        layout.addStretch()

        return widget

    def create_results_tab(self):
        """إنشاء تبويب عرض النتائج"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النتائج
        self.results_title = QLabel("📋 نتائج التقرير")
        self.results_title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        self.results_title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        layout.addWidget(self.results_title)

        # جدول النتائج
        self.results_table = QTableWidget()
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.results_table)

        # ملخص النتائج
        self.results_summary = QTextEdit()
        self.results_summary.setMaximumHeight(150)
        self.results_summary.setReadOnly(True)
        self.results_summary.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 10px;
                font-size: 13px;
            }
        """)
        layout.addWidget(self.results_summary)

        return widget

    def generate_report(self):
        """إنشاء التقرير"""
        # تحديد نوع التقرير
        report_types = {
            "تقرير الزيارات": "visits",
            "تقرير الأدوية الأكثر استخداماً": "medications",
            "تقرير التشخيصات الأكثر شيوعاً": "diagnoses",
            "تقرير المرضى": "patients"
        }

        report_type = report_types[self.report_type_combo.currentText()]
        start_date = self.start_date.date().toString('yyyy-MM-dd')
        end_date = self.end_date.date().toString('yyyy-MM-dd')

        # إظهار شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.generate_button.setEnabled(False)

        # إنشاء خيط منفصل لإنشاء التقرير
        self.report_thread = ReportGeneratorThread(
            self.db_manager, report_type, start_date, end_date
        )
        self.report_thread.progress_updated.connect(self.progress_bar.setValue)
        self.report_thread.report_ready.connect(self.display_report)
        self.report_thread.error_occurred.connect(self.handle_error)
        self.report_thread.start()

    def display_report(self, report_data):
        """عرض نتائج التقرير"""
        self.current_report_data = report_data

        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)
        self.generate_button.setEnabled(True)

        # عرض النتائج حسب نوع التقرير
        if report_data['type'] == 'visits':
            self.display_visits_report(report_data)
        elif report_data['type'] == 'medications':
            self.display_medications_report(report_data)
        elif report_data['type'] == 'diagnoses':
            self.display_diagnoses_report(report_data)
        elif report_data['type'] == 'patients':
            self.display_patients_report(report_data)

    def display_visits_report(self, data):
        """عرض تقرير الزيارات"""
        # إعداد الجدول
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels([
            "التاريخ", "اسم المريض", "رقم الملف", "التشخيص", "العلاج"
        ])

        # ملء البيانات
        self.results_table.setRowCount(len(data['visit_details']))
        for row, visit in enumerate(data['visit_details']):
            self.results_table.setItem(row, 0, QTableWidgetItem(visit[0]))
            self.results_table.setItem(row, 1, QTableWidgetItem(visit[1]))
            self.results_table.setItem(row, 2, QTableWidgetItem(visit[2]))
            self.results_table.setItem(row, 3, QTableWidgetItem(visit[3] or ""))
            treatment = visit[4] or ""
            if len(treatment) > 50:
                treatment = treatment[:50] + "..."
            self.results_table.setItem(row, 4, QTableWidgetItem(treatment))

        # تحديث الملخص
        summary = f"""
📊 ملخص تقرير الزيارات ({data['period']})

📈 إجمالي الزيارات: {data['total_visits']} زيارة

🔝 أكثر التشخيصات شيوعاً:
"""
        for i, (diagnosis, count) in enumerate(data['top_diagnoses'][:5], 1):
            summary += f"{i}. {diagnosis}: {count} مرة\n"

        self.results_summary.setPlainText(summary)
        self.results_title.setText(f"📋 تقرير الزيارات - {data['period']}")

    def display_medications_report(self, data):
        """عرض تقرير الأدوية"""
        # إعداد الجدول
        self.results_table.setColumnCount(3)
        self.results_table.setHorizontalHeaderLabels([
            "اسم الدواء", "عدد مرات الاستخدام", "الوصف"
        ])

        # ملء البيانات
        self.results_table.setRowCount(len(data['top_medications']))
        for row, medication in enumerate(data['top_medications']):
            self.results_table.setItem(row, 0, QTableWidgetItem(medication[0]))
            self.results_table.setItem(row, 1, QTableWidgetItem(str(medication[1])))
            self.results_table.setItem(row, 2, QTableWidgetItem(medication[2] or ""))

        # تحديث الملخص
        summary = f"""
💊 ملخص تقرير الأدوية

📊 إجمالي الأدوية المسجلة: {data['total_medications']} دواء
📈 إجمالي مرات الاستخدام: {data['total_usage']} مرة

🔝 أكثر 5 أدوية استخداماً:
"""
        for i, (name, count, _) in enumerate(data['top_medications'][:5], 1):
            summary += f"{i}. {name}: {count} مرة\n"

        self.results_summary.setPlainText(summary)
        self.results_title.setText("💊 تقرير الأدوية الأكثر استخداماً")

    def display_diagnoses_report(self, data):
        """عرض تقرير التشخيصات"""
        # إعداد الجدول
        self.results_table.setColumnCount(3)
        self.results_table.setHorizontalHeaderLabels([
            "التشخيص", "عدد مرات الاستخدام", "الوصف"
        ])

        # ملء البيانات
        self.results_table.setRowCount(len(data['top_diagnoses']))
        for row, diagnosis in enumerate(data['top_diagnoses']):
            self.results_table.setItem(row, 0, QTableWidgetItem(diagnosis[0]))
            self.results_table.setItem(row, 1, QTableWidgetItem(str(diagnosis[1])))
            self.results_table.setItem(row, 2, QTableWidgetItem(diagnosis[2] or ""))

        # تحديث الملخص
        summary = f"""
🔍 ملخص تقرير التشخيصات

📊 إجمالي التشخيصات المسجلة: {data['total_diagnoses']} تشخيص
📈 إجمالي مرات الاستخدام: {data['total_usage']} مرة

🔝 أكثر 5 تشخيصات شيوعاً:
"""
        for i, (name, count, _) in enumerate(data['top_diagnoses'][:5], 1):
            summary += f"{i}. {name}: {count} مرة\n"

        self.results_summary.setPlainText(summary)
        self.results_title.setText("🔍 تقرير التشخيصات الأكثر شيوعاً")

    def display_patients_report(self, data):
        """عرض تقرير المرضى"""
        # إعداد الجدول
        self.results_table.setColumnCount(3)
        self.results_table.setHorizontalHeaderLabels([
            "اسم المريض", "رقم الملف", "عدد الزيارات"
        ])

        # ملء البيانات
        self.results_table.setRowCount(len(data['most_visited_patients']))
        for row, patient in enumerate(data['most_visited_patients']):
            self.results_table.setItem(row, 0, QTableWidgetItem(patient[0]))
            self.results_table.setItem(row, 1, QTableWidgetItem(patient[1]))
            self.results_table.setItem(row, 2, QTableWidgetItem(str(patient[2])))

        # تحديث الملخص
        summary = f"""
👥 ملخص تقرير المرضى

📊 إجمالي المرضى المسجلين: {data['total_patients']} مريض

👫 توزيع المرضى حسب الجنس:
"""
        for gender, count in data['patients_by_gender']:
            gender_text = "ذكر" if gender == "male" else "أنثى" if gender == "female" else "غير محدد"
            summary += f"• {gender_text}: {count} مريض\n"

        self.results_summary.setPlainText(summary)
        self.results_title.setText("👥 تقرير المرضى")

    def handle_error(self, error_message):
        """التعامل مع الأخطاء"""
        self.progress_bar.setVisible(False)
        self.generate_button.setEnabled(True)
        QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير:\n{error_message}")

    def print_report(self):
        """طباعة التقرير"""
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير للطباعة")
            return

        try:
            from utils.print_manager import PRINT_SUPPORT_AVAILABLE
            if not PRINT_SUPPORT_AVAILABLE:
                QMessageBox.warning(self, "خطأ", "مكتبة الطباعة غير متوفرة")
                return

            from PyQt6.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt6.QtGui import QTextDocument

            # إنشاء HTML للتقرير
            report_html = self.generate_report_html()

            # إعداد الطابعة
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            self.setup_printer_page_size(printer)

            # فتح نافذة الطباعة
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec() == QPrintDialog.DialogCode.Accepted:
                document = QTextDocument()
                document.setHtml(report_html)
                document.print(printer)
                QMessageBox.information(self, "نجح", "تم طباعة التقرير بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير: {str(e)}")

    def setup_printer_page_size(self, printer):
        """إعداد حجم الصفحة للطابعة"""
        try:
            from PyQt6.QtGui import QPageSize
            page_size = QPageSize(QPageSize.PageSizeId.A4)
            printer.setPageSize(page_size)
        except ImportError:
            try:
                printer.setPageSize(QPrinter.PageSize.A4)
            except AttributeError:
                try:
                    from PyQt6.QtPrintSupport import QPrinter
                    printer.setPageSize(QPrinter.A4)
                except:
                    print("تحذير: لا يمكن تعيين حجم الصفحة")
        except Exception as e:
            print(f"خطأ في إعداد حجم الصفحة: {e}")

    def generate_report_html(self):
        """إنشاء HTML للتقرير"""
        if not self.current_report_data:
            return "<html><body><h1>لا يوجد تقرير</h1></body></html>"

        report_type = self.current_report_data.get('type', 'تقرير')
        title = self.get_report_title(report_type)

        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <style>
                @page {{
                    size: A4;
                    margin: 2cm;
                }}
                body {{
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    margin: 0;
                    padding: 0;
                    text-align: right;
                    font-size: 12px;
                    line-height: 1.6;
                    color: #333;
                }}
                .header {{
                    text-align: center;
                    border-bottom: 3px solid #2c3e50;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    padding: 20px;
                    border-radius: 10px;
                }}
                .title {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }}
                .subtitle {{
                    font-size: 16px;
                    color: #7f8c8d;
                }}
                .summary {{
                    background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 100%);
                    padding: 20px;
                    border-radius: 12px;
                    margin-bottom: 25px;
                    border: 2px solid #3498db;
                }}
                .summary h3 {{
                    margin-top: 0;
                    color: #2c3e50;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 8px;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    background: white;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                th {{
                    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
                    color: white;
                    padding: 15px;
                    text-align: center;
                    font-weight: bold;
                }}
                td {{
                    padding: 12px;
                    border-bottom: 1px solid #e9ecef;
                    text-align: center;
                }}
                tr:nth-child(even) {{
                    background-color: #f8f9fa;
                }}
                tr:hover {{
                    background-color: #e3f2fd;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 2px solid #bdc3c7;
                    font-size: 10px;
                    color: #7f8c8d;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">🏥 {title}</div>
                <div class="subtitle">نظام إدارة العيادة الطبية</div>
                <div class="subtitle">📅 {datetime.now().strftime('%Y-%m-%d %H:%M')}</div>
            </div>

            {self.generate_summary_html()}
            {self.generate_table_html()}

            <div class="footer">
                <p>🏥 تم إنشاء هذا التقرير بواسطة نظام إدارة العيادة الطبية</p>
                <p>📅 تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            </div>
        </body>
        </html>
        """

        return html

    def get_report_title(self, report_type):
        """الحصول على عنوان التقرير"""
        titles = {
            'visits': 'تقرير الزيارات',
            'medications': 'تقرير الأدوية',
            'diagnoses': 'تقرير التشخيصات',
            'patients': 'تقرير المرضى'
        }
        return titles.get(report_type, 'تقرير')

    def generate_summary_html(self):
        """إنشاء HTML للملخص"""
        summary = self.current_report_data.get('summary', {})
        if not summary:
            return ""

        html = '<div class="summary"><h3>📊 ملخص التقرير</h3>'
        for key, value in summary.items():
            html += f'<p><strong>{key}:</strong> {value}</p>'
        html += '</div>'

        return html

    def generate_table_html(self):
        """إنشاء HTML للجدول"""
        data = self.current_report_data.get('data', [])
        if not data:
            return "<p>لا توجد بيانات للعرض</p>"

        # الحصول على العناوين من أول صف
        headers = list(data[0].keys()) if data else []

        html = '<table><thead><tr>'
        for header in headers:
            html += f'<th>{header}</th>'
        html += '</tr></thead><tbody>'

        for row in data:
            html += '<tr>'
            for header in headers:
                value = row.get(header, '')
                html += f'<td>{value}</td>'
            html += '</tr>'

        html += '</tbody></table>'
        return html

    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير لتصديره")
            return

        try:
            from PyQt6.QtWidgets import QFileDialog
            from PyQt6.QtPrintSupport import QPrinter
            from PyQt6.QtGui import QTextDocument

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير", f"تقرير_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء HTML للتقرير
                report_html = self.generate_report_html()

                # إعداد الطابعة للـ PDF
                printer = QPrinter(QPrinter.PrinterMode.HighResolution)
                printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)
                printer.setOutputFileName(file_path)
                self.setup_printer_page_size(printer)

                # إنشاء المستند وطباعته
                document = QTextDocument()
                document.setHtml(report_html)
                document.print(printer)

                QMessageBox.information(self, "نجح", f"تم حفظ التقرير في:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير: {str(e)}")