# نظام إدارة العيادة الطبية

نظام شامل لإدارة العيادات الطبية مطور باستخدام Python و PyQt6 مع قاعدة بيانات SQLite.

## المميزات الحالية

### ✅ المميزات المكتملة:
- **نظام تسجيل الدخول**: دعم مستخدمين (طبيب/سكرتير) مع صلاحيات مختلفة
- **إدارة المرضى**: إضافة، تعديل، حذف، والبحث في المرضى
- **قاعدة بيانات SQLite**: تخزين آمن ومنظم للبيانات
- **واجهة عصرية**: تصميم احترافي يدعم اللغة العربية
- **النسخ الاحتياطي**: نظام النسخ الاحتياطي التلقائي

### 🚧 المميزات قيد التطوير:
- إدارة زيارات المرضى
- نظام وصف العلاج مع الاقتراحات التلقائية
- طباعة الوصفات والتقارير
- زيارات اليوم
- نظام الشبكة المحلية
- إدارة الأدوية

## متطلبات النظام

- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل البرنامج
```bash
python main.py
```

## بيانات تسجيل الدخول الافتراضية

### الطبيب:
- **اسم المستخدم**: doctor
- **كلمة المرور**: doctor123
- **النوع**: طبيب

### السكرتير:
- **اسم المستخدم**: secretary
- **كلمة المرور**: secretary123
- **النوع**: سكرتير

## هيكل المشروع

```
clinic_management/
├── main.py                 # الملف الرئيسي للتطبيق
├── requirements.txt        # متطلبات Python
├── database/
│   ├── __init__.py
│   └── database.py        # إدارة قاعدة البيانات
├── models/
│   ├── __init__.py
│   ├── patient.py         # نموذج المرضى
│   ├── visit.py           # نموذج الزيارات
│   ├── medication.py      # نموذج الأدوية
│   └── user.py            # نموذج المستخدمين
├── ui/
│   ├── __init__.py
│   ├── login_window.py    # نافذة تسجيل الدخول
│   ├── main_window.py     # النافذة الرئيسية
│   └── patients_widget.py # واجهة إدارة المرضى
└── clinic_database.db     # قاعدة البيانات (تُنشأ تلقائياً)
```

## الاستخدام

### 1. تسجيل الدخول
- اختر نوع المستخدم (طبيب أو سكرتير)
- أدخل اسم المستخدم وكلمة المرور
- اضغط "تسجيل الدخول"

### 2. إدارة المرضى
- انقر على "إدارة المرضى" من القائمة الجانبية
- لإضافة مريض جديد: اضغط "إضافة مريض جديد"
- للبحث: استخدم مربع البحث في الأعلى
- للتعديل: انقر مرتين على المريض أو اختره واضغط "تعديل"
- للحذف: اختر المريض واضغط "حذف"

### 3. النسخ الاحتياطي
- يتم إنشاء نسخة احتياطية تلقائياً عند إغلاق البرنامج
- يمكن إنشاء نسخة احتياطية يدوياً من قائمة "ملف" > "إنشاء نسخة احتياطية"

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:

- **users**: المستخدمين وصلاحياتهم
- **patients**: بيانات المرضى
- **visits**: زيارات المرضى
- **visit_images**: الصور المرفقة بالزيارات
- **medications**: قاعدة بيانات الأدوية
- **settings**: إعدادات النظام

## الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية.

---

**ملاحظة**: هذا الإصدار الأولي من النظام. المزيد من المميزات قيد التطوير.
