# 🏥 نظام إدارة العيادة الطبية الاحترافي

نظام شامل ومتطور لإدارة العيادات الطبية مطور باستخدام Python و PyQt6 مع قاعدة بيانات SQLite. يتميز بتصميم عصري وواجهة احترافية تدعم اللغة العربية بالكامل.

## المميزات الحالية

### ✅ المميزات المكتملة:

#### 🔐 **نظام المصادقة والمستخدمين:**
- **تسجيل دخول عصري**: واجهة احترافية مع تأثيرات بصرية وتدرجات لونية
- **ثلاثة أنواع مستخدمين**: طبيب، سكرتير، مدير مع صلاحيات مختلفة
- **إدارة المستخدمين**: إضافة وتعديل وحذف المستخدمين (للمدير فقط)
- **تغيير كلمة المرور**: نظام آمن لتغيير كلمات المرور

#### 👥 **إدارة المرضى:**
- **واجهة احترافية**: تصميم عصري مع بطاقات وظلال
- **إدارة شاملة**: إضافة، تعديل، حذف، والبحث المتقدم
- **رقم ملف تلقائي**: توليد أرقام الملفات تلقائياً
- **معلومات مفصلة**: الاسم، الجنس، العمر، العنوان، الهاتف

#### 📋 **إدارة الزيارات:**
- **زيارات شاملة**: تسجيل البيانات الطبية (الوزن، السكر، الضغط)
- **الصور المرفقة**: إمكانية إرفاق صور التحاليل والأشعة
- **وصف العلاج الذكي**: محرر متقدم مع اقتراحات تلقائية للأدوية
- **تاريخ الزيارات**: عرض تاريخ كامل لزيارات كل مريض

#### 📅 **زيارات اليوم:**
- **عرض فوري**: زيارات اليوم مع تحديث تلقائي كل 5 دقائق
- **إحصائيات سريعة**: عدد الزيارات والمعلومات الأساسية
- **طباعة التقارير**: تقارير يومية قابلة للطباعة والحفظ

#### 💊 **إدارة الأدوية:**
- **قاعدة بيانات ذكية**: حفظ الأدوية مع عداد الاستخدام
- **الأدوية الشائعة**: عرض الأدوية الأكثر استخداماً
- **بحث متقدم**: البحث السريع في قاعدة بيانات الأدوية

#### 🖨️ **نظام الطباعة الاحترافي:**
- **طباعة الوصفات**: تصميم احترافي مع شعار العيادة
- **حفظ PDF**: إمكانية حفظ الوصفات كملفات PDF
- **إعدادات قابلة للتخصيص**: تنسيق الخط، الهوامش، والمحتوى
- **معاينة الطباعة**: معاينة قبل الطباعة

#### ⚙️ **نظام الإعدادات المتقدم:**
- **معلومات العيادة**: الاسم، العنوان، الهاتف، اسم الطبيب
- **إدارة الشعار**: رفع وعرض شعار العيادة
- **إعدادات الشبكة**: تحضير للربط عبر الشبكة المحلية
- **النسخ الاحتياطي**: إعدادات النسخ التلقائي والفوري

#### 🎨 **التصميم والواجهة:**
- **تصميم عصري**: بطاقات، ظلال، حواف دائرية، تدرجات لونية
- **دعم اللغة العربية**: RTL كامل مع خطوط عربية واضحة
- **أيقونات تعبيرية**: استخدام الإيموجي لتحسين تجربة المستخدم
- **ألوان احترافية**: نظام ألوان متناسق ومريح للعين

### 🚧 المميزات قيد التطوير:
- نظام وصف العلاج مع الاقتراحات التلقائية للأدوية
- طباعة الوصفات الطبية بتنسيق PDF احترافي
- نظام الشبكة المحلية للعمل متعدد الأجهزة
- تقارير متقدمة وإحصائيات

## متطلبات النظام

- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل البرنامج
```bash
python main.py
```

## 🔑 بيانات تسجيل الدخول الافتراضية

### 👨‍⚕️ الطبيب:
- **اسم المستخدم**: doctor
- **كلمة المرور**: doctor123
- **الصلاحيات**: كاملة (مرضى، زيارات، أدوية، تقارير)

### 👩‍💼 السكرتير:
- **اسم المستخدم**: secretary
- **كلمة المرور**: secretary123
- **الصلاحيات**: محدودة (مرضى، زيارات، تقارير)

### 👨‍💼 المدير:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **الصلاحيات**: إدارية (مستخدمين، إعدادات، نسخ احتياطي)

## هيكل المشروع

```
clinic_management/
├── main.py                 # الملف الرئيسي للتطبيق
├── requirements.txt        # متطلبات Python
├── database/
│   ├── __init__.py
│   └── database.py        # إدارة قاعدة البيانات
├── models/
│   ├── __init__.py
│   ├── patient.py         # نموذج المرضى
│   ├── visit.py           # نموذج الزيارات
│   ├── medication.py      # نموذج الأدوية
│   └── user.py            # نموذج المستخدمين
├── ui/
│   ├── __init__.py
│   ├── login_window.py        # نافذة تسجيل الدخول
│   ├── main_window.py         # النافذة الرئيسية
│   ├── patients_widget.py     # واجهة إدارة المرضى
│   ├── visits_widget.py       # واجهة إدارة زيارات المريض
│   ├── today_visits_widget.py # واجهة زيارات اليوم
│   ├── medications_widget.py  # واجهة إدارة الأدوية
│   └── settings_widget.py     # واجهة الإعدادات
└── clinic_database.db     # قاعدة البيانات (تُنشأ تلقائياً)
```

## الاستخدام

### 1. تسجيل الدخول
- اختر نوع المستخدم (طبيب أو سكرتير)
- أدخل اسم المستخدم وكلمة المرور
- اضغط "تسجيل الدخول"

### 2. إدارة المرضى
- انقر على "إدارة المرضى" من القائمة الجانبية
- لإضافة مريض جديد: اضغط "إضافة مريض جديد"
- للبحث: استخدم مربع البحث في الأعلى
- للتعديل: انقر مرتين على المريض أو اختره واضغط "تعديل"
- للحذف: اختر المريض واضغط "حذف"
- لعرض زيارات المريض: اختر المريض واضغط "عرض الزيارات"

### 3. إدارة الزيارات
- من صفحة المريض، اضغط "عرض الزيارات"
- لإضافة زيارة جديدة: اضغط "إضافة زيارة جديدة"
- أدخل البيانات الطبية: الوزن، السكر، الضغط، الملاحظات
- يمكن إرفاق صور طبية (تحاليل، أشعة)
- اكتب وصف العلاج في المربع المخصص

### 4. زيارات اليوم
- انقر على "زيارات اليوم" لعرض زيارات اليوم الحالي
- يمكن البحث في الزيارات حسب اسم المريض
- يمكن طباعة تقرير يومي بجميع الزيارات

### 5. إدارة الأدوية (للطبيب فقط)
- انقر على "إدارة الأدوية" من القائمة الجانبية
- أضف أدوية جديدة مع الوصف
- عرض الأدوية الأكثر استخداماً
- البحث في قاعدة بيانات الأدوية

### 6. الإعدادات
- انقر على "الإعدادات" لتكوين النظام
- **معلومات العيادة**: اسم العيادة، العنوان، الهاتف، اسم الطبيب
- **الشعار**: رفع وإدارة شعار العيادة
- **الشبكة**: إعدادات الشبكة المحلية (مستقل/رئيسي/فرعي)
- **النسخ الاحتياطي**: تفعيل النسخ التلقائي وتحديد المجلد
- **عام**: إعدادات اللغة والنظام

### 7. النسخ الاحتياطي
- يتم إنشاء نسخة احتياطية تلقائياً عند إغلاق البرنامج
- يمكن إنشاء نسخة احتياطية يدوياً من قائمة "ملف" > "إنشاء نسخة احتياطية"
- من الإعدادات يمكن إنشاء نسخة فورية أو استعادة نسخة سابقة

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:

- **users**: المستخدمين وصلاحياتهم
- **patients**: بيانات المرضى
- **visits**: زيارات المرضى
- **visit_images**: الصور المرفقة بالزيارات
- **medications**: قاعدة بيانات الأدوية
- **settings**: إعدادات النظام

## الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية.

---

**ملاحظة**: هذا الإصدار الأولي من النظام. المزيد من المميزات قيد التطوير.
