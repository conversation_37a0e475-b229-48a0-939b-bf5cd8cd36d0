# نظام إدارة العيادة الطبية

نظام شامل لإدارة العيادات الطبية مطور باستخدام Python و PyQt6 مع قاعدة بيانات SQLite.

## المميزات الحالية

### ✅ المميزات المكتملة:
- **نظام تسجيل الدخول**: دعم مستخدمين (طبيب/سكرتير) مع صلاحيات مختلفة
- **إدارة المرضى**: إضافة، تعديل، حذف، والبحث في المرضى مع واجهة احترافية
- **إدارة زيارات المرضى**: تسجيل زيارات شاملة مع البيانات الطبية والصور المرفقة
- **زيارات اليوم**: عرض وإدارة زيارات اليوم مع إمكانية طباعة التقارير
- **إدارة الأدوية**: قاعدة بيانات شاملة للأدوية مع نظام الاستخدام الشائع
- **نظام الإعدادات**: إعدادات شاملة للعيادة، الشبكة، والنسخ الاحتياطي
- **قاعدة بيانات SQLite**: تخزين آمن ومنظم للبيانات
- **واجهة عصرية**: تصميم احترافي يدعم اللغة العربية بالكامل
- **النسخ الاحتياطي**: نظام النسخ الاحتياطي التلقائي والفوري

### 🚧 المميزات قيد التطوير:
- نظام وصف العلاج مع الاقتراحات التلقائية للأدوية
- طباعة الوصفات الطبية بتنسيق PDF احترافي
- نظام الشبكة المحلية للعمل متعدد الأجهزة
- تقارير متقدمة وإحصائيات

## متطلبات النظام

- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل البرنامج
```bash
python main.py
```

## بيانات تسجيل الدخول الافتراضية

### الطبيب:
- **اسم المستخدم**: doctor
- **كلمة المرور**: doctor123
- **النوع**: طبيب

### السكرتير:
- **اسم المستخدم**: secretary
- **كلمة المرور**: secretary123
- **النوع**: سكرتير

## هيكل المشروع

```
clinic_management/
├── main.py                 # الملف الرئيسي للتطبيق
├── requirements.txt        # متطلبات Python
├── database/
│   ├── __init__.py
│   └── database.py        # إدارة قاعدة البيانات
├── models/
│   ├── __init__.py
│   ├── patient.py         # نموذج المرضى
│   ├── visit.py           # نموذج الزيارات
│   ├── medication.py      # نموذج الأدوية
│   └── user.py            # نموذج المستخدمين
├── ui/
│   ├── __init__.py
│   ├── login_window.py        # نافذة تسجيل الدخول
│   ├── main_window.py         # النافذة الرئيسية
│   ├── patients_widget.py     # واجهة إدارة المرضى
│   ├── visits_widget.py       # واجهة إدارة زيارات المريض
│   ├── today_visits_widget.py # واجهة زيارات اليوم
│   ├── medications_widget.py  # واجهة إدارة الأدوية
│   └── settings_widget.py     # واجهة الإعدادات
└── clinic_database.db     # قاعدة البيانات (تُنشأ تلقائياً)
```

## الاستخدام

### 1. تسجيل الدخول
- اختر نوع المستخدم (طبيب أو سكرتير)
- أدخل اسم المستخدم وكلمة المرور
- اضغط "تسجيل الدخول"

### 2. إدارة المرضى
- انقر على "إدارة المرضى" من القائمة الجانبية
- لإضافة مريض جديد: اضغط "إضافة مريض جديد"
- للبحث: استخدم مربع البحث في الأعلى
- للتعديل: انقر مرتين على المريض أو اختره واضغط "تعديل"
- للحذف: اختر المريض واضغط "حذف"
- لعرض زيارات المريض: اختر المريض واضغط "عرض الزيارات"

### 3. إدارة الزيارات
- من صفحة المريض، اضغط "عرض الزيارات"
- لإضافة زيارة جديدة: اضغط "إضافة زيارة جديدة"
- أدخل البيانات الطبية: الوزن، السكر، الضغط، الملاحظات
- يمكن إرفاق صور طبية (تحاليل، أشعة)
- اكتب وصف العلاج في المربع المخصص

### 4. زيارات اليوم
- انقر على "زيارات اليوم" لعرض زيارات اليوم الحالي
- يمكن البحث في الزيارات حسب اسم المريض
- يمكن طباعة تقرير يومي بجميع الزيارات

### 5. إدارة الأدوية (للطبيب فقط)
- انقر على "إدارة الأدوية" من القائمة الجانبية
- أضف أدوية جديدة مع الوصف
- عرض الأدوية الأكثر استخداماً
- البحث في قاعدة بيانات الأدوية

### 6. الإعدادات
- انقر على "الإعدادات" لتكوين النظام
- **معلومات العيادة**: اسم العيادة، العنوان، الهاتف، اسم الطبيب
- **الشعار**: رفع وإدارة شعار العيادة
- **الشبكة**: إعدادات الشبكة المحلية (مستقل/رئيسي/فرعي)
- **النسخ الاحتياطي**: تفعيل النسخ التلقائي وتحديد المجلد
- **عام**: إعدادات اللغة والنظام

### 7. النسخ الاحتياطي
- يتم إنشاء نسخة احتياطية تلقائياً عند إغلاق البرنامج
- يمكن إنشاء نسخة احتياطية يدوياً من قائمة "ملف" > "إنشاء نسخة احتياطية"
- من الإعدادات يمكن إنشاء نسخة فورية أو استعادة نسخة سابقة

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:

- **users**: المستخدمين وصلاحياتهم
- **patients**: بيانات المرضى
- **visits**: زيارات المرضى
- **visit_images**: الصور المرفقة بالزيارات
- **medications**: قاعدة بيانات الأدوية
- **settings**: إعدادات النظام

## الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية.

---

**ملاحظة**: هذا الإصدار الأولي من النظام. المزيد من المميزات قيد التطوير.
