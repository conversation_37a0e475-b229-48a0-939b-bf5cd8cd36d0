from datetime import datetime, date
import sqlite3
from database.database import DatabaseManager

class Visit:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def add_visit(self, patient_id, visit_date=None, weight=None, blood_sugar=None,
                  blood_pressure=None, diagnosis=None, notes=None, treatment_description=None):
        """إضافة زيارة جديدة"""
        if visit_date is None:
            visit_date = date.today().isoformat()

        conn = self.db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO visits (patient_id, visit_date, weight, blood_sugar,
                                  blood_pressure, diagnosis, notes, treatment_description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (patient_id, visit_date, weight, blood_sugar, blood_pressure, diagnosis, notes, treatment_description))

            visit_id = cursor.lastrowid
            conn.commit()
            return visit_id
        finally:
            conn.close()
    
    def get_visit(self, visit_id):
        """الحصول على بيانات زيارة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT v.*, p.full_name, p.file_number 
            FROM visits v
            JOIN patients p ON v.patient_id = p.id
            WHERE v.id = ?
        ''', (visit_id,))
        result = cursor.fetchone()
        conn.close()
        return dict(result) if result else None
    
    def update_visit(self, visit_id, **kwargs):
        """تحديث بيانات زيارة"""
        if not kwargs:
            return False
        
        # إضافة تاريخ التحديث
        kwargs['updated_at'] = datetime.now().isoformat()
        
        # بناء استعلام التحديث
        set_clause = ', '.join([f"{key} = ?" for key in kwargs.keys()])
        values = list(kwargs.values()) + [visit_id]
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute(f'UPDATE visits SET {set_clause} WHERE id = ?', values)
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        return affected_rows > 0
    
    def delete_visit(self, visit_id):
        """حذف زيارة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM visits WHERE id = ?', (visit_id,))
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        return affected_rows > 0
    
    def get_patient_visits(self, patient_id, limit=50, offset=0):
        """الحصول على زيارات مريض"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT * FROM visits 
            WHERE patient_id = ?
            ORDER BY visit_date DESC, created_at DESC
            LIMIT ? OFFSET ?
        ''', (patient_id, limit, offset))
        
        results = cursor.fetchall()
        conn.close()
        return [dict(row) for row in results]
    
    def get_today_visits(self):
        """الحصول على زيارات اليوم"""
        today = date.today().isoformat()
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT v.*, p.full_name, p.file_number, p.phone
            FROM visits v
            JOIN patients p ON v.patient_id = p.id
            WHERE v.visit_date = ?
            ORDER BY v.created_at DESC
        ''', (today,))
        
        results = cursor.fetchall()
        conn.close()
        return [dict(row) for row in results]
    
    def get_visits_by_date_range(self, start_date, end_date):
        """الحصول على الزيارات في فترة زمنية"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT v.*, p.full_name, p.file_number, p.phone
            FROM visits v
            JOIN patients p ON v.patient_id = p.id
            WHERE v.visit_date BETWEEN ? AND ?
            ORDER BY v.visit_date DESC, v.created_at DESC
        ''', (start_date, end_date))
        
        results = cursor.fetchall()
        conn.close()
        return [dict(row) for row in results]
    
    def add_visit_image(self, visit_id, image_path, image_type=None, description=None):
        """إضافة صورة لزيارة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO visit_images (visit_id, image_path, image_type, description)
                VALUES (?, ?, ?, ?)
            ''', (visit_id, image_path, image_type, description))
            
            image_id = cursor.lastrowid
            conn.commit()
            return image_id
        finally:
            conn.close()
    
    def get_visit_images(self, visit_id):
        """الحصول على صور الزيارة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT * FROM visit_images 
            WHERE visit_id = ?
            ORDER BY created_at
        ''', (visit_id,))
        
        results = cursor.fetchall()
        conn.close()
        return [dict(row) for row in results]
    
    def delete_visit_image(self, image_id):
        """حذف صورة من زيارة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM visit_images WHERE id = ?', (image_id,))
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        return affected_rows > 0
