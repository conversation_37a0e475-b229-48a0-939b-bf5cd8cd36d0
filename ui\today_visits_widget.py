from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QTableWidget, QTableWidgetItem, QPushButton,
                            QMessageBox, QHeaderView, QFrame, QDateEdit,
                            QComboBox, QLineEdit)
from PyQt6.QtCore import Qt, QDate, QTimer
from PyQt6.QtGui import QFont
from datetime import datetime, date

class TodayVisitsWidget(QWidget):
    """ويدجت زيارات اليوم"""
    
    def __init__(self, visit_model, patient_model, parent=None):
        super().__init__(parent)
        self.visit_model = visit_model
        self.patient_model = patient_model
        self.current_visits = []
        self.init_ui()
        self.load_today_visits()
        
        # تحديث تلقائي كل 5 دقائق
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_today_visits)
        self.timer.start(300000)  # 5 دقائق
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان والتاريخ
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        
        # العنوان الرئيسي
        title_layout = QHBoxLayout()
        
        title_label = QLabel("📅 زيارات اليوم")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; border: none; padding: 0;")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # التاريخ الحالي
        current_date = QLabel(datetime.now().strftime("%A, %d %B %Y"))
        current_date.setFont(QFont("Arial", 12))
        current_date.setStyleSheet("color: #7f8c8d; border: none; padding: 0;")
        title_layout.addWidget(current_date)
        
        header_layout.addLayout(title_layout)
        
        # إحصائيات سريعة
        stats_layout = QHBoxLayout()
        
        self.total_visits_label = QLabel("إجمالي الزيارات: 0")
        self.total_visits_label.setStyleSheet("color: #27ae60; font-weight: bold; border: none; padding: 5px;")
        stats_layout.addWidget(self.total_visits_label)
        
        stats_layout.addStretch()
        
        self.last_update_label = QLabel("آخر تحديث: --")
        self.last_update_label.setStyleSheet("color: #95a5a6; font-size: 10px; border: none; padding: 0;")
        stats_layout.addWidget(self.last_update_label)
        
        header_layout.addLayout(stats_layout)
        
        layout.addWidget(header_frame)
        
        # أدوات التحكم والفلترة
        controls_frame = QFrame()
        controls_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 10px;
            }
        """)
        controls_layout = QHBoxLayout(controls_frame)
        
        # اختيار التاريخ
        date_label = QLabel("📅 التاريخ:")
        date_label.setStyleSheet("color: #2c3e50; font-weight: bold; border: none; padding: 0;")
        controls_layout.addWidget(date_label)
        
        self.date_picker = QDateEdit()
        self.date_picker.setDate(QDate.currentDate())
        self.date_picker.setCalendarPopup(True)
        self.date_picker.setFixedHeight(35)
        self.date_picker.dateChanged.connect(self.load_visits_by_date)
        controls_layout.addWidget(self.date_picker)
        
        controls_layout.addStretch()
        
        # البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setStyleSheet("color: #2c3e50; font-weight: bold; border: none; padding: 0;")
        controls_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في أسماء المرضى...")
        self.search_input.setFixedWidth(200)
        self.search_input.setFixedHeight(35)
        self.search_input.textChanged.connect(self.filter_visits)
        controls_layout.addWidget(self.search_input)
        
        # أزرار التحكم
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFixedHeight(35)
        refresh_btn.clicked.connect(self.load_today_visits)
        controls_layout.addWidget(refresh_btn)
        
        print_btn = QPushButton("🖨️ طباعة التقرير")
        print_btn.setFixedHeight(35)
        print_btn.clicked.connect(self.print_report)
        controls_layout.addWidget(print_btn)
        
        layout.addWidget(controls_frame)
        
        # جدول الزيارات
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(15, 15, 15, 15)
        
        table_title = QLabel("📋 قائمة الزيارات")
        table_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        table_title.setStyleSheet("color: #2c3e50; border: none; padding: 0; margin-bottom: 10px;")
        table_layout.addWidget(table_title)
        
        self.visits_table = QTableWidget()
        self.visits_table.setColumnCount(7)
        self.visits_table.setHorizontalHeaderLabels([
            "الوقت", "اسم المريض", "رقم الملف", "الهاتف", "الوزن", "السكر", "الضغط"
        ])
        
        # تعديل عرض الأعمدة
        header = self.visits_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        
        self.visits_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.visits_table.setAlternatingRowColors(True)
        self.visits_table.setMinimumHeight(400)
        
        table_layout.addWidget(self.visits_table)
        layout.addWidget(table_frame)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLineEdit, QDateEdit {
                padding: 8px 12px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus, QDateEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: none;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)
    
    def load_today_visits(self):
        """تحميل زيارات اليوم"""
        try:
            self.current_visits = self.visit_model.get_today_visits()
            self.update_table()
            self.update_stats()
            self.last_update_label.setText(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل زيارات اليوم: {str(e)}")
    
    def load_visits_by_date(self):
        """تحميل الزيارات حسب التاريخ المحدد"""
        selected_date = self.date_picker.date().toString('yyyy-MM-dd')
        try:
            if selected_date == date.today().isoformat():
                self.current_visits = self.visit_model.get_today_visits()
            else:
                self.current_visits = self.visit_model.get_visits_by_date_range(selected_date, selected_date)
            
            self.update_table()
            self.update_stats()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الزيارات: {str(e)}")
    
    def filter_visits(self):
        """فلترة الزيارات حسب البحث"""
        search_term = self.search_input.text().lower()
        
        for row in range(self.visits_table.rowCount()):
            patient_name = self.visits_table.item(row, 1).text().lower()
            file_number = self.visits_table.item(row, 2).text().lower()
            
            if search_term in patient_name or search_term in file_number:
                self.visits_table.setRowHidden(row, False)
            else:
                self.visits_table.setRowHidden(row, True)
    
    def update_table(self):
        """تحديث جدول الزيارات"""
        self.visits_table.setRowCount(len(self.current_visits))
        
        for row, visit in enumerate(self.current_visits):
            # الوقت
            created_time = datetime.strptime(visit['created_at'], '%Y-%m-%d %H:%M:%S').strftime('%H:%M')
            self.visits_table.setItem(row, 0, QTableWidgetItem(created_time))
            
            # اسم المريض
            self.visits_table.setItem(row, 1, QTableWidgetItem(visit['full_name']))
            
            # رقم الملف
            self.visits_table.setItem(row, 2, QTableWidgetItem(visit['file_number']))
            
            # الهاتف
            phone = visit.get('phone', '') or ''
            self.visits_table.setItem(row, 3, QTableWidgetItem(phone))
            
            # الوزن
            weight = f"{visit['weight']} كجم" if visit['weight'] else ""
            self.visits_table.setItem(row, 4, QTableWidgetItem(weight))
            
            # السكر
            sugar = f"{visit['blood_sugar']}" if visit['blood_sugar'] else ""
            self.visits_table.setItem(row, 5, QTableWidgetItem(sugar))
            
            # الضغط
            pressure = visit['blood_pressure'] or ""
            self.visits_table.setItem(row, 6, QTableWidgetItem(pressure))
    
    def update_stats(self):
        """تحديث الإحصائيات"""
        total_visits = len(self.current_visits)
        self.total_visits_label.setText(f"إجمالي الزيارات: {total_visits}")
    
    def print_report(self):
        """طباعة تقرير الزيارات"""
        if not self.current_visits:
            QMessageBox.information(self, "تنبيه", "لا توجد زيارات للطباعة")
            return
        
        selected_date = self.date_picker.date().toString('yyyy/MM/dd')
        
        try:
            # إنشاء تقرير بسيط
            report_content = f"""
تقرير زيارات العيادة
التاريخ: {selected_date}
إجمالي الزيارات: {len(self.current_visits)}

قائمة الزيارات:
{'='*50}
"""
            
            for i, visit in enumerate(self.current_visits, 1):
                created_time = datetime.strptime(visit['created_at'], '%Y-%m-%d %H:%M:%S').strftime('%H:%M')
                weight = f"{visit['weight']} كجم" if visit['weight'] else "غير محدد"
                sugar = f"{visit['blood_sugar']}" if visit['blood_sugar'] else "غير محدد"
                pressure = visit['blood_pressure'] or "غير محدد"
                
                report_content += f"""
{i}. {visit['full_name']} (ملف: {visit['file_number']})
   الوقت: {created_time}
   الوزن: {weight} | السكر: {sugar} | الضغط: {pressure}
   الهاتف: {visit.get('phone', 'غير محدد')}
{'-'*30}
"""
            
            # حفظ التقرير في ملف
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"تقرير_زيارات_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            QMessageBox.information(self, "نجح", f"تم حفظ التقرير في الملف:\n{filename}")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {str(e)}")
    
    def closeEvent(self, event):
        """إيقاف المؤقت عند إغلاق الويدجت"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        event.accept()
