from PyQt6.QtWidgets import (
    QMain<PERSON><PERSON>ow, QStackedWidget, QMessageBox,
    QMenuBar, QMenu
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QAction

from .patients_window import PatientsWindow
from .visits_window import VisitsWindow
from .today_visits_window import TodayVisitsWindow
from .settings_window import SettingsWindow
from ..database import db

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the main window UI."""
        self.setWindowTitle("نظام إدارة العيادة الطبية")
        self.setMinimumSize(1024, 768)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create stacked widget for managing different screens
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)

        # Initialize all windows
        self.patients_window = PatientsWindow(self)
        self.visits_window = VisitsWindow(self)
        self.today_visits_window = TodayVisitsWindow(self)
        self.settings_window = SettingsWindow(self)

        # Add windows to stacked widget
        self.stacked_widget.addWidget(self.patients_window)
        self.stacked_widget.addWidget(self.visits_window)
        self.stacked_widget.addWidget(self.today_visits_window)
        self.stacked_widget.addWidget(self.settings_window)
        
        # Set up menu
        self.setup_menu()

    def setup_menu(self):
        """Set up the menu bar."""
        menubar = self.menuBar()
        
        # Main menu
        main_menu = menubar.addMenu("القائمة الرئيسية")
        
        # Patients action
        patients_action = QAction("المرضى", self)
        patients_action.triggered.connect(self.show_patients)
        main_menu.addAction(patients_action)
        
        # Today's visits action
        today_visits_action = QAction("زيارات اليوم", self)
        today_visits_action.triggered.connect(self.show_today_visits)
        main_menu.addAction(today_visits_action)
        
        # Settings action
        settings_action = QAction("الإعدادات", self)
        settings_action.triggered.connect(self.show_settings)
        main_menu.addAction(settings_action)
        
        main_menu.addSeparator()
        
        # Exit action
        exit_action = QAction("خروج", self)
        exit_action.triggered.connect(self.close)
        main_menu.addAction(exit_action)

    def show_patients(self):
        """Show the patients window."""
        self.stacked_widget.setCurrentWidget(self.patients_window)

    def show_visits(self, patient_id=None, patient_name=None):
        """Show the visits window for a specific patient."""
        if patient_id and patient_name:
            self.visits_window.set_patient(patient_id, patient_name)
        self.stacked_widget.setCurrentWidget(self.visits_window)

    def show_today_visits(self):
        """Show today's visits window."""
        self.today_visits_window.refresh_visits()
        self.stacked_widget.setCurrentWidget(self.today_visits_window)

    def show_settings(self):
        """Show the settings window."""
        self.stacked_widget.setCurrentWidget(self.settings_window)

    def closeEvent(self, event):
        """Handle window close event."""
        reply = QMessageBox.question(
            self,
            "تأكيد الخروج",
            "هل أنت متأكد من رغبتك في الخروج من التطبيق؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()
