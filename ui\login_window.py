import sys
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QMessageBox, QFrame,
                            QApplication, QComboBox, QGraphicsDropShadowEffect)
from PyQt6.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QPixmap, QIcon, QColor, QPalette, QLinearGradient
from database.database import DatabaseManager
from models.user import User

class LoginWindow(QWidget):
    login_successful = pyqtSignal(dict)  # إشارة نجاح تسجيل الدخول
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.user_model = User(self.db_manager)
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة العيادة الطبية - تسجيل الدخول")
        self.setFixedSize(500, 650)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # تطبيق الستايل العصري
        self.setStyleSheet("""
            QWidget {
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                direction: rtl;
            }

            QFrame#mainFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 20px;
            }

            QFrame#loginCard {
                background-color: rgba(255, 255, 255, 0.95);
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }

            QLabel#appTitle {
                color: #2c3e50;
                font-size: 28px;
                font-weight: bold;
                margin: 0;
                padding: 10px;
            }

            QLabel#appSubtitle {
                color: #7f8c8d;
                font-size: 16px;
                margin: 0;
                padding: 5px;
            }

            QLabel#loginTitle {
                color: #34495e;
                font-size: 20px;
                font-weight: bold;
                margin: 20px 0 10px 0;
            }

            QLabel#fieldLabel {
                color: #2c3e50;
                font-size: 14px;
                font-weight: 600;
                margin: 15px 0 5px 0;
                padding: 0;
            }

            QLineEdit {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 15px 20px;
                font-size: 14px;
                color: #2c3e50;
                margin: 5px 0 10px 0;
                min-height: 20px;
            }

            QLineEdit:focus {
                border-color: #3498db;
                background-color: #ffffff;
                box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            }

            QLineEdit:hover {
                border-color: #bdc3c7;
                background-color: #ffffff;
            }

            QComboBox {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 15px 20px;
                font-size: 14px;
                color: #2c3e50;
                margin: 5px 0 10px 0;
                min-height: 20px;
            }

            QComboBox:focus {
                border-color: #3498db;
                background-color: #ffffff;
            }

            QComboBox:hover {
                border-color: #bdc3c7;
                background-color: #ffffff;
            }

            QComboBox::drop-down {
                border: none;
                width: 30px;
                margin-right: 10px;
            }

            QComboBox::down-arrow {
                image: none;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 8px solid #7f8c8d;
                margin-right: 8px;
            }

            QPushButton#loginButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 18px;
                font-size: 16px;
                font-weight: bold;
                margin: 25px 0 15px 0;
                min-height: 25px;
            }

            QPushButton#loginButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2980b9, stop:1 #21618c);
                transform: translateY(-2px);
            }

            QPushButton#loginButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #21618c, stop:1 #1a4f72);
                transform: translateY(0px);
            }

            QLabel#infoLabel {
                color: #95a5a6;
                font-size: 11px;
                background-color: rgba(236, 240, 241, 0.8);
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
                line-height: 1.4;
            }
        """)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(0)

        # الإطار الرئيسي مع التدرج
        main_frame = QFrame()
        main_frame.setObjectName("mainFrame")
        main_frame_layout = QVBoxLayout(main_frame)
        main_frame_layout.setContentsMargins(0, 0, 0, 0)
        main_frame_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # بطاقة تسجيل الدخول
        login_card = QFrame()
        login_card.setObjectName("loginCard")
        login_card.setFixedSize(400, 550)

        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        login_card.setGraphicsEffect(shadow)

        card_layout = QVBoxLayout(login_card)
        card_layout.setContentsMargins(40, 40, 40, 40)
        card_layout.setSpacing(0)

        # عنوان التطبيق
        app_title = QLabel("🏥 إدارة العيادة الطبية")
        app_title.setObjectName("appTitle")
        app_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        card_layout.addWidget(app_title)

        app_subtitle = QLabel("نظام إدارة شامل للعيادات الطبية")
        app_subtitle.setObjectName("appSubtitle")
        app_subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        card_layout.addWidget(app_subtitle)

        # مساحة فاصلة
        card_layout.addSpacing(30)

        # عنوان تسجيل الدخول
        login_title = QLabel("🔐 تسجيل الدخول")
        login_title.setObjectName("loginTitle")
        login_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        card_layout.addWidget(login_title)

        # حقول الإدخال
        # اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم")
        username_label.setObjectName("fieldLabel")
        card_layout.addWidget(username_label)

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.returnPressed.connect(self.login)
        card_layout.addWidget(self.username_input)

        # كلمة المرور
        password_label = QLabel("🔒 كلمة المرور")
        password_label.setObjectName("fieldLabel")
        card_layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.returnPressed.connect(self.login)
        card_layout.addWidget(self.password_input)

        # نوع المستخدم
        role_label = QLabel("👨‍⚕️ نوع المستخدم")
        role_label.setObjectName("fieldLabel")
        card_layout.addWidget(role_label)

        self.role_combo = QComboBox()
        self.role_combo.addItems(["طبيب", "سكرتير", "مدير"])
        card_layout.addWidget(self.role_combo)

        # زر تسجيل الدخول
        self.login_button = QPushButton("🚀 تسجيل الدخول")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.login)
        card_layout.addWidget(self.login_button)

        # معلومات المستخدمين الافتراضيين
        info_label = QLabel("""
📋 المستخدمين الافتراضيين:

👨‍⚕️ الطبيب: doctor / doctor123
👩‍💼 السكرتير: secretary / secretary123
👨‍💼 المدير: admin / admin123
        """.strip())
        info_label.setObjectName("infoLabel")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        card_layout.addWidget(info_label)

        main_frame_layout.addWidget(login_card)
        main_layout.addWidget(main_frame)
        
        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        selected_role_text = self.role_combo.currentText()

        # تحويل النص العربي إلى الدور الإنجليزي
        role_mapping = {
            "طبيب": "doctor",
            "سكرتير": "secretary",
            "مدير": "admin"
        }
        selected_role = role_mapping.get(selected_role_text, "doctor")

        if not username or not password:
            self.show_error_message("خطأ في البيانات", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        try:
            # التحقق من صحة بيانات المستخدم
            user_data = self.user_model.authenticate(username, password)

            if user_data:
                # التحقق من تطابق الدور
                if user_data['role'] == selected_role:
                    # نجح تسجيل الدخول
                    self.show_success_animation()
                    self.login_successful.emit(user_data)
                    self.close()
                else:
                    role_names = {"doctor": "طبيب", "secretary": "سكرتير", "admin": "مدير"}
                    role_name = role_names.get(user_data['role'], "غير محدد")
                    self.show_error_message("خطأ في الدور",
                                          f"هذا المستخدم مسجل كـ {role_name}")
            else:
                self.show_error_message("خطأ في تسجيل الدخول",
                                      "اسم المستخدم أو كلمة المرور غير صحيحة")

        except Exception as e:
            self.show_error_message("خطأ في النظام", f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")

    def show_error_message(self, title, message):
        """عرض رسالة خطأ مع تصميم محسن"""
        msg = QMessageBox(self)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Icon.Warning)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: white;
                border-radius: 10px;
            }
            QMessageBox QLabel {
                color: #2c3e50;
                font-size: 14px;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 20px;
                font-weight: bold;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        msg.exec()

    def show_success_animation(self):
        """عرض تأثير نجاح تسجيل الدخول"""
        self.login_button.setText("✅ تم بنجاح!")
        self.login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #27ae60, stop:1 #2ecc71);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 18px;
                font-size: 16px;
                font-weight: bold;
                margin: 25px 0 15px 0;
                min-height: 25px;
            }
        """)
    
    def keyPressEvent(self, event):
        """التعامل مع ضغط المفاتيح"""
        if event.key() == Qt.Key.Key_Escape:
            self.close()
        super().keyPressEvent(event)
