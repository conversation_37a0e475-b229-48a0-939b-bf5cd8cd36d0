import sys
from PyQt6.QtWidgets import (<PERSON>W<PERSON>t, QVBoxLayout, QHBox<PERSON>ayout, QLabel, 
                            QLineEdit, QPushButton, QMessageBox, QFrame,
                            QApplication, QComboBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap, QIcon
from database.database import DatabaseManager
from models.user import User

class LoginWindow(QWidget):
    login_successful = pyqtSignal(dict)  # إشارة نجاح تسجيل الدخول
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.user_model = User(self.db_manager)
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - إدارة العيادة الطبية")
        self.setFixedSize(400, 500)
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QFrame#loginFrame {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #ddd;
            }
            QLabel#titleLabel {
                color: #2c3e50;
                font-size: 24px;
                font-weight: bold;
                margin: 20px 0;
            }
            QLabel#subtitleLabel {
                color: #7f8c8d;
                font-size: 14px;
                margin-bottom: 30px;
            }
            QLineEdit {
                padding: 12px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 14px;
                margin: 5px 0;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QComboBox {
                padding: 12px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 14px;
                margin: 5px 0;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QPushButton#loginButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 5px;
                font-size: 16px;
                font-weight: bold;
                margin: 20px 0;
            }
            QPushButton#loginButton:hover {
                background-color: #2980b9;
            }
            QPushButton#loginButton:pressed {
                background-color: #21618c;
            }
            QLabel {
                color: #2c3e50;
                font-size: 12px;
                margin: 5px 0;
            }
        """)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setObjectName("loginFrame")
        login_frame.setFixedSize(350, 400)
        
        frame_layout = QVBoxLayout(login_frame)
        frame_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.setSpacing(10)
        
        # العنوان
        title_label = QLabel("إدارة العيادة الطبية")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(title_label)
        
        subtitle_label = QLabel("تسجيل الدخول")
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(subtitle_label)
        
        # حقول الإدخال
        username_label = QLabel("اسم المستخدم:")
        frame_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.returnPressed.connect(self.login)
        frame_layout.addWidget(self.username_input)
        
        password_label = QLabel("كلمة المرور:")
        frame_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.returnPressed.connect(self.login)
        frame_layout.addWidget(self.password_input)
        
        role_label = QLabel("نوع المستخدم:")
        frame_layout.addWidget(role_label)
        
        self.role_combo = QComboBox()
        self.role_combo.addItems(["طبيب", "سكرتير"])
        frame_layout.addWidget(self.role_combo)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.login)
        frame_layout.addWidget(self.login_button)
        
        # معلومات افتراضية
        info_label = QLabel("المستخدمين الافتراضيين:\nالطبيب: doctor / doctor123\nالسكرتير: secretary / secretary123")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("color: #7f8c8d; font-size: 10px; margin-top: 20px;")
        frame_layout.addWidget(info_label)
        
        main_layout.addWidget(login_frame)
        self.setLayout(main_layout)
        
        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        selected_role = "doctor" if self.role_combo.currentText() == "طبيب" else "secretary"
        
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            # التحقق من صحة بيانات المستخدم
            user_data = self.user_model.authenticate(username, password)
            
            if user_data:
                # التحقق من تطابق الدور
                if user_data['role'] == selected_role:
                    # نجح تسجيل الدخول
                    self.login_successful.emit(user_data)
                    self.close()
                else:
                    role_name = "طبيب" if user_data['role'] == "doctor" else "سكرتير"
                    QMessageBox.warning(self, "خطأ في الدور", 
                                      f"هذا المستخدم مسجل كـ {role_name}")
            else:
                QMessageBox.warning(self, "خطأ في تسجيل الدخول", 
                                  "اسم المستخدم أو كلمة المرور غير صحيحة")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")
    
    def keyPressEvent(self, event):
        """التعامل مع ضغط المفاتيح"""
        if event.key() == Qt.Key.Key_Escape:
            self.close()
        super().keyPressEvent(event)
