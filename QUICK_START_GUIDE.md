# 🚀 دليل البدء السريع - نظام إدارة العيادة الطبية

## 📋 **متطلبات التشغيل**

### **متطلبات النظام:**
- Windows 10/11 أو macOS أو Linux
- Python 3.8 أو أحدث
- ذاكرة RAM: 4 جيجابايت على الأقل
- مساحة تخزين: 500 ميجابايت

### **المكتبات المطلوبة:**
```bash
pip install -r requirements.txt
```

---

## ⚡ **التشغيل السريع**

### **1. تشغيل البرنامج:**
```bash
python main.py
```

### **2. تسجيل الدخول:**
استخدم أحد الحسابات التالية:

| النوع | اسم المستخدم | كلمة المرور | الصلاحيات |
|-------|--------------|-------------|-----------|
| 👨‍⚕️ طبيب | `doctor` | `doctor123` | كاملة |
| 👩‍💼 سكرتير | `secretary` | `secretary123` | محدودة |
| 👨‍💼 مدير | `admin` | `admin123` | إدارية |

---

## 🏠 **لوحة التحكم الرئيسية**

عند تسجيل الدخول، ستظهر **لوحة التحكم الذكية** التي تعرض:

### **📊 الإحصائيات الفورية:**
- إجمالي المرضى المسجلين
- زيارات اليوم الحالي
- زيارات الأسبوع الحالي
- عدد الأدوية في النظام

### **📈 الرسوم البيانية:**
- أكثر الأدوية استخداماً
- أكثر التشخيصات شيوعاً

### **🕒 الأنشطة الأخيرة:**
- آخر الزيارات المسجلة
- تحديث تلقائي كل 30 ثانية

---

## 👥 **إدارة المرضى**

### **إضافة مريض جديد:**
1. انقر على "👥 إدارة المرضى"
2. انقر على "➕ إضافة مريض جديد"
3. املأ البيانات المطلوبة:
   - الاسم الكامل
   - الجنس (ذكر/أنثى)
   - العمر
   - العنوان
   - رقم الهاتف
4. انقر "💾 حفظ"

### **البحث عن مريض:**
- استخدم مربع البحث في أعلى الصفحة
- يمكن البحث بالاسم أو رقم الملف أو الهاتف

---

## 📋 **إدارة الزيارات**

### **إضافة زيارة جديدة:**
1. اختر مريض من قائمة المرضى
2. انقر على "➕ إضافة زيارة جديدة"
3. املأ البيانات الطبية:
   - **التاريخ**: تاريخ الزيارة
   - **الوزن**: بالكيلوجرام
   - **السكر**: بـ mg/dL
   - **ضغط الدم**: مثل "120/80"
   - **🔍 التشخيص**: التشخيص الطبي
   - **📝 الملاحظات**: ملاحظات إضافية
   - **💊 وصف العلاج**: الأدوية والعلاج

### **📎 إرفاق الصور:**
- انقر "📎 إرفاق صورة"
- اختر صور التحاليل أو الأشعة
- يدعم: JPG, PNG, PDF

---

## 💊 **كتابة الوصفات الذكية**

### **✨ التنسيق التلقائي:**
1. اكتب الوصفة بأي شكل، مثل:
   ```
   برستول يوميا
   فيتامين د مرة واحدة
   باراسيتامول 500 عند الحاجة
   ```

2. انقر "✨ تنسيق تلقائي"

3. ستصبح الوصفة منسقة:
   ```
   1. برستول - مرة يومياً
   2. فيتامين د 1000 وحدة - مرة يومياً  
   3. باراسيتامول 500 مجم - عند الحاجة
   ```

### **📝 المحرر المتقدم:**
- انقر "📝 محرر متقدم" لفتح محرر الوصفات الذكي
- يحتوي على:
  - اقتراحات تلقائية للأدوية
  - قوالب جاهزة للحالات الشائعة
  - أدوات تنسيق النص

---

## 🖨️ **طباعة الوصفات**

### **طباعة احترافية:**
1. في صفحة الزيارة، انقر "🖨️ طباعة وصفة"
2. ستفتح نافذة الطباعة المتقدمة
3. يمكنك:
   - تخصيص معلومات العيادة
   - تعديل تنسيق الخط والهوامش
   - معاينة الوصفة قبل الطباعة
   - حفظ كملف PDF

---

## 📊 **التقارير والإحصائيات**

### **إنشاء التقارير:**
1. انقر على "📊 التقارير"
2. اختر نوع التقرير:
   - **تقرير الزيارات**: زيارات خلال فترة محددة
   - **تقرير الأدوية**: أكثر الأدوية استخداماً
   - **تقرير التشخيصات**: أكثر التشخيصات شيوعاً
   - **تقرير المرضى**: إحصائيات المرضى

3. حدد الفترة الزمنية
4. انقر "📊 إنشاء التقرير"

### **عرض النتائج:**
- الجداول التفاعلية
- الملخصات الإحصائية
- إمكانية التصدير (قريباً)

---

## 👥 **إدارة المستخدمين** (للمدير فقط)

### **إضافة مستخدم جديد:**
1. انقر "👥 إدارة المستخدمين"
2. انقر "➕ إضافة مستخدم"
3. املأ البيانات:
   - اسم المستخدم
   - الاسم الكامل
   - الدور (طبيب/سكرتير/مدير)
   - كلمة المرور

### **تغيير كلمة المرور:**
1. اختر مستخدم من القائمة
2. انقر "🔒 تغيير كلمة المرور"
3. أدخل كلمة المرور القديمة والجديدة

---

## ⚙️ **الإعدادات**

### **معلومات العيادة:**
- اسم العيادة
- اسم الطبيب
- العنوان والهاتف
- شعار العيادة

### **النسخ الاحتياطي:**
- نسخ احتياطي تلقائي عند الإغلاق
- نسخ احتياطي فوري
- اختيار مجلد النسخ الاحتياطي

---

## 🔧 **نصائح للاستخدام الأمثل**

### **⚡ اختصارات مفيدة:**
- **البحث السريع**: اكتب في مربع البحث مباشرة
- **النقر المزدوج**: لتعديل أي عنصر في الجداول
- **التحديث التلقائي**: لوحة التحكم تحدث تلقائياً

### **📱 أفضل الممارسات:**
1. **النسخ الاحتياطي**: تأكد من تفعيل النسخ الاحتياطي التلقائي
2. **كلمات المرور**: استخدم كلمات مرور قوية
3. **التحديث المنتظم**: راجع الإحصائيات في لوحة التحكم يومياً
4. **التنظيم**: استخدم التشخيصات المعيارية للحصول على تقارير دقيقة

### **🆘 حل المشاكل الشائعة:**
- **بطء في التحميل**: تحقق من حجم قاعدة البيانات
- **مشاكل الطباعة**: تأكد من تثبيت طابعة افتراضية
- **أخطاء الحفظ**: تحقق من صلاحيات الكتابة في مجلد البرنامج

---

## 📞 **الدعم والمساعدة**

### **الملفات المرجعية:**
- `README.md`: دليل التثبيت الكامل
- `FEATURES_SUMMARY.md`: ملخص شامل للمميزات

### **معلومات النظام:**
- **الإصدار**: 1.0
- **تاريخ الإصدار**: 2024
- **اللغات المدعومة**: العربية والإنجليزية

---

## 🎉 **مبروك!**

أنت الآن جاهز لاستخدام نظام إدارة العيادة الطبية بكفاءة عالية! 

النظام مصمم ليكون **بديهياً وسهل الاستخدام** مع توفير جميع المميزات المتقدمة التي تحتاجها لإدارة عيادتك بشكل احترافي. 🏥✨
