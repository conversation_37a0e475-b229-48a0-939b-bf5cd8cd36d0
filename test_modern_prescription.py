#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التصميم العصري والمضغوط للوصفة الطبية
تم إنشاؤه في: 2024-12-26
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# استيراد مدير الطباعة
from utils.print_manager import PrescriptionPrintDialog

class ModernPrescriptionTester(QMainWindow):
    """واجهة اختبار التصميم العصري للوصفة الطبية"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🧪 اختبار التصميم العصري للوصفة الطبية")
        self.setGeometry(200, 200, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # العنوان
        title = QLabel("🎨 اختبار التصميم العصري والمضغوط للوصفة الطبية")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("color: #2c3e50; padding: 20px; background: #ecf0f1; border-radius: 8px; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # الوصف
        description = QTextEdit()
        description.setMaximumHeight(150)
        description.setPlainText("""
التحديثات الجديدة في التصميم العصري:
✅ أحجام خطوط متوسطة ومناسبة للطباعة (11-18px)
✅ تصميم مضغوط يناسب صفحة A4 واحدة
✅ ألوان عصرية ومتدرجة
✅ تنسيق Grid للمعلومات 
✅ هوامش صغيرة (8mm) لتوفير المساحة
✅ تباعد مضغوط بين العناصر
✅ رقم وصفة في الزاوية
✅ تصميم مبسط وأنيق للطباعة

الهدف: وصفة طبية عصرية وجذابة تناسب صفحة واحدة فقط.
        """)
        description.setStyleSheet("background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 10px;")
        layout.addWidget(description)
        
        # أزرار الاختبار
        test_button = QPushButton("🖨️ اختبار الوصفة العصرية")
        test_button.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            }
        """)
        test_button.clicked.connect(self.test_modern_prescription)
        layout.addWidget(test_button)
        
        # سجل النتائج
        self.log = QTextEdit()
        self.log.setStyleSheet("background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;")
        layout.addWidget(self.log)
    
    def test_modern_prescription(self):
        """اختبار الوصفة العصرية"""
        self.log.append(f"🚀 بدء اختبار التصميم العصري - {datetime.now().strftime('%H:%M:%S')}")
        
        try:
            # بيانات وهمية للاختبار
            patient_data = {
                'full_name': 'أحمد محمد علي السعيد',
                'file_number': 'P-2024-001234',
                'age': 35,
                'gender': 'male'
            }
            
            visit_data = {
                'diagnosis': 'التهاب في الجهاز التنفسي العلوي مع أعراض نزلة برد',
                'treatment': """1. أموكسيسيلين 500 مجم - كبسولة كل 8 ساعات لمدة 7 أيام
2. باراسيتامول 500 مجم - قرص عند الحاجة للألم أو الحمى
3. شراب للسعال - ملعقة صغيرة 3 مرات يومياً
4. فيتامين سي 1000 مجم - قرص يومياً لمدة أسبوعين
5. الراحة التامة وشرب السوائل الدافئة"""
            }
            
            clinic_settings = {
                'clinic_name': 'عيادة الدكتور أحمد الطبية',
                'doctor_name': 'د. أحمد محمد الأطباء',
                'clinic_address': 'شارع الملك فهد، الرياض، المملكة العربية السعودية',
                'clinic_phone': '+966 11 123 4567'
            }
            
            # فتح نافذة الطباعة العصرية
            print_dialog = PrescriptionPrintDialog(patient_data, visit_data, clinic_settings, self)
            result = print_dialog.exec()
            
            if result:
                self.log.append("✅ تم فتح نافذة الطباعة العصرية بنجاح")
                self.log.append("🎨 التصميم الجديد يتضمن:")
                self.log.append("  • خطوط متوسطة الحجم ومقروءة")
                self.log.append("  • تصميم مضغوط في صفحة واحدة")
                self.log.append("  • ألوان عصرية ومتدرجة")
                self.log.append("  • تنسيق Grid للمعلومات")
                self.log.append("  • هوامش مضغوطة (8mm)")
                self.log.append("  • رقم وصفة في الزاوية")
            else:
                self.log.append("❌ تم إلغاء العملية")
                
        except Exception as e:
            self.log.append(f"❌ خطأ في الاختبار: {str(e)}")
            import traceback
            self.log.append(f"تفاصيل الخطأ: {traceback.format_exc()}")
        
        self.log.append("=" * 50)

def main():
    """تشغيل اختبار التصميم العصري"""
    app = QApplication(sys.argv)
    
    # تطبيق خط عربي
    app.setStyle('Fusion')
    
    tester = ModernPrescriptionTester()
    tester.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
