#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.login_window import LoginWindow
from ui.main_window import MainWindow
from database.database import DatabaseManager

class ClinicApp:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.setup_app()
        self.login_window = None
        self.main_window = None
        
    def setup_app(self):
        """إعداد التطبيق"""
        # تعيين اسم التطبيق
        self.app.setApplicationName("إدارة العيادة الطبية")
        self.app.setApplicationVersion("1.0")
        self.app.setOrganizationName("عيادة طبية")
        
        # تعيين الخط الافتراضي
        font = QFont("Segoe UI", 10)
        self.app.setFont(font)
        
        # دعم اللغة العربية
        self.app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # تعيين ستايل عام
        self.app.setStyleSheet("""
            QMessageBox {
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
            }
            QMessageBox QLabel {
                color: #2c3e50;
            }
            QMessageBox QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background-color: #2980b9;
            }
            QMessageBox QPushButton:pressed {
                background-color: #21618c;
            }
        """)
    
    def init_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            db_manager = DatabaseManager()
            return True
        except Exception as e:
            QMessageBox.critical(None, "خطأ في قاعدة البيانات", 
                               f"فشل في تهيئة قاعدة البيانات:\n{str(e)}")
            return False
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        self.login_window = LoginWindow()
        self.login_window.login_successful.connect(self.on_login_successful)
        self.login_window.show()
    
    def on_login_successful(self, user_data):
        """التعامل مع نجاح تسجيل الدخول"""
        try:
            # إغلاق نافذة تسجيل الدخول
            if self.login_window:
                self.login_window.close()
                self.login_window = None
            
            # فتح النافذة الرئيسية
            self.main_window = MainWindow(user_data)
            self.main_window.show()
            
        except Exception as e:
            QMessageBox.critical(None, "خطأ", 
                               f"فشل في فتح النافذة الرئيسية:\n{str(e)}")
    
    def run(self):
        """تشغيل التطبيق"""
        # تهيئة قاعدة البيانات
        if not self.init_database():
            return 1
        
        # عرض نافذة تسجيل الدخول
        self.show_login()
        
        # تشغيل حلقة الأحداث
        return self.app.exec()

def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء وتشغيل التطبيق
        clinic_app = ClinicApp()
        return clinic_app.run()
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
