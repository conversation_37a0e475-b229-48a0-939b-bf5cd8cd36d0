from PyQt6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QMessageBox, QFrame,
                            QFormLayout, QGroupBox, QFileDialog, QCheckBox,
                            QComboBox, QSpinBox, QTextEdit, QTabWidget)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QPixmap
import os

class SettingsWidget(QWidget):
    """ويدجت الإعدادات"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        header_layout = QHBoxLayout(header_frame)
        
        title_label = QLabel("⚙️ إعدادات النظام")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; border: none; padding: 0;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        layout.addWidget(header_frame)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب معلومات العيادة
        clinic_tab = self.create_clinic_tab()
        self.tabs.addTab(clinic_tab, "🏥 معلومات العيادة")
        
        # تبويب الشبكة
        network_tab = self.create_network_tab()
        self.tabs.addTab(network_tab, "🌐 إعدادات الشبكة")
        
        # تبويب النسخ الاحتياطي
        backup_tab = self.create_backup_tab()
        self.tabs.addTab(backup_tab, "💾 النسخ الاحتياطي")
        
        # تبويب عام
        general_tab = self.create_general_tab()
        self.tabs.addTab(general_tab, "🔧 إعدادات عامة")
        
        layout.addWidget(self.tabs)
        
        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 15px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        
        save_button = QPushButton("💾 حفظ الإعدادات")
        save_button.setFixedHeight(40)
        save_button.setFixedWidth(150)
        save_button.clicked.connect(self.save_settings)
        buttons_layout.addWidget(save_button)
        
        reset_button = QPushButton("🔄 استعادة الافتراضي")
        reset_button.setFixedHeight(40)
        reset_button.setFixedWidth(150)
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        reset_button.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(reset_button)
        
        buttons_layout.addStretch()
        
        test_connection_button = QPushButton("🔗 اختبار الاتصال")
        test_connection_button.setFixedHeight(40)
        test_connection_button.setFixedWidth(150)
        test_connection_button.clicked.connect(self.test_connection)
        buttons_layout.addWidget(test_connection_button)
        
        layout.addWidget(buttons_frame)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QTabWidget::pane {
                border: 1px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
            }
            QTabWidget::tab-bar {
                alignment: center;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #bdc3c7;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            QLineEdit, QComboBox, QSpinBox, QTextEdit {
                padding: 8px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QTextEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QCheckBox {
                font-weight: bold;
                color: #2c3e50;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #3498db;
                border-radius: 3px;
                background-color: #3498db;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggM0w0IDdMMiA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
        """)
    
    def create_clinic_tab(self):
        """إنشاء تبويب معلومات العيادة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # معلومات العيادة
        clinic_group = QGroupBox("🏥 معلومات العيادة")
        clinic_form = QFormLayout(clinic_group)
        
        self.clinic_name_input = QLineEdit()
        self.clinic_name_input.setFixedHeight(35)
        clinic_form.addRow("اسم العيادة:", self.clinic_name_input)
        
        self.clinic_address_input = QTextEdit()
        self.clinic_address_input.setFixedHeight(80)
        clinic_form.addRow("عنوان العيادة:", self.clinic_address_input)
        
        self.clinic_phone_input = QLineEdit()
        self.clinic_phone_input.setFixedHeight(35)
        clinic_form.addRow("هاتف العيادة:", self.clinic_phone_input)
        
        layout.addWidget(clinic_group)
        
        # معلومات الطبيب
        doctor_group = QGroupBox("👨‍⚕️ معلومات الطبيب")
        doctor_form = QFormLayout(doctor_group)
        
        self.doctor_name_input = QLineEdit()
        self.doctor_name_input.setFixedHeight(35)
        doctor_form.addRow("اسم الطبيب:", self.doctor_name_input)
        
        layout.addWidget(doctor_group)
        
        # الشعار
        logo_group = QGroupBox("🖼️ شعار العيادة")
        logo_layout = QVBoxLayout(logo_group)
        
        logo_buttons_layout = QHBoxLayout()
        
        self.logo_path_input = QLineEdit()
        self.logo_path_input.setFixedHeight(35)
        self.logo_path_input.setReadOnly(True)
        logo_buttons_layout.addWidget(self.logo_path_input)
        
        browse_logo_button = QPushButton("📁 تصفح")
        browse_logo_button.setFixedHeight(35)
        browse_logo_button.clicked.connect(self.browse_logo)
        logo_buttons_layout.addWidget(browse_logo_button)
        
        remove_logo_button = QPushButton("🗑️ إزالة")
        remove_logo_button.setFixedHeight(35)
        remove_logo_button.clicked.connect(self.remove_logo)
        logo_buttons_layout.addWidget(remove_logo_button)
        
        logo_layout.addLayout(logo_buttons_layout)
        
        # معاينة الشعار
        self.logo_preview = QLabel("لا يوجد شعار")
        self.logo_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.logo_preview.setFixedHeight(100)
        self.logo_preview.setStyleSheet("""
            QLabel {
                border: 2px dashed #bdc3c7;
                border-radius: 6px;
                color: #7f8c8d;
                background-color: #f8f9fa;
            }
        """)
        logo_layout.addWidget(self.logo_preview)
        
        layout.addWidget(logo_group)
        layout.addStretch()
        
        return widget
    
    def create_network_tab(self):
        """إنشاء تبويب إعدادات الشبكة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # وضع الشبكة
        network_group = QGroupBox("🌐 إعدادات الشبكة المحلية")
        network_form = QFormLayout(network_group)
        
        self.network_mode_combo = QComboBox()
        self.network_mode_combo.addItems([
            "جهاز مستقل (Standalone)",
            "جهاز رئيسي (Master)",
            "جهاز فرعي (Client)"
        ])
        self.network_mode_combo.setFixedHeight(35)
        network_form.addRow("وضع الشبكة:", self.network_mode_combo)
        
        self.master_ip_input = QLineEdit()
        self.master_ip_input.setFixedHeight(35)
        self.master_ip_input.setPlaceholderText("مثال: *************")
        network_form.addRow("IP الجهاز الرئيسي:", self.master_ip_input)
        
        self.network_port_input = QSpinBox()
        self.network_port_input.setRange(1000, 65535)
        self.network_port_input.setValue(5000)
        self.network_port_input.setFixedHeight(35)
        network_form.addRow("منفذ الشبكة:", self.network_port_input)
        
        layout.addWidget(network_group)
        
        # معلومات الشبكة
        info_group = QGroupBox("ℹ️ معلومات الشبكة")
        info_layout = QVBoxLayout(info_group)
        
        self.network_info_label = QLabel("سيتم عرض معلومات الشبكة هنا...")
        self.network_info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 10px;
                color: #495057;
            }
        """)
        info_layout.addWidget(self.network_info_label)
        
        layout.addWidget(info_group)
        layout.addStretch()
        
        return widget
    
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # إعدادات النسخ الاحتياطي
        backup_group = QGroupBox("💾 إعدادات النسخ الاحتياطي")
        backup_form = QFormLayout(backup_group)
        
        self.auto_backup_checkbox = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        backup_form.addRow(self.auto_backup_checkbox)
        
        backup_folder_layout = QHBoxLayout()
        self.backup_folder_input = QLineEdit()
        self.backup_folder_input.setFixedHeight(35)
        self.backup_folder_input.setReadOnly(True)
        backup_folder_layout.addWidget(self.backup_folder_input)
        
        browse_backup_button = QPushButton("📁 تصفح")
        browse_backup_button.setFixedHeight(35)
        browse_backup_button.clicked.connect(self.browse_backup_folder)
        backup_folder_layout.addWidget(browse_backup_button)
        
        backup_form.addRow("مجلد النسخ الاحتياطي:", backup_folder_layout)
        
        layout.addWidget(backup_group)
        
        # عمليات النسخ الاحتياطي
        operations_group = QGroupBox("🔧 عمليات النسخ الاحتياطي")
        operations_layout = QVBoxLayout(operations_group)
        
        backup_now_button = QPushButton("💾 إنشاء نسخة احتياطية الآن")
        backup_now_button.setFixedHeight(40)
        backup_now_button.clicked.connect(self.create_backup_now)
        operations_layout.addWidget(backup_now_button)
        
        restore_button = QPushButton("📥 استعادة من نسخة احتياطية")
        restore_button.setFixedHeight(40)
        restore_button.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        restore_button.clicked.connect(self.restore_backup)
        operations_layout.addWidget(restore_button)
        
        layout.addWidget(operations_group)
        layout.addStretch()
        
        return widget
    
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # إعدادات عامة
        general_group = QGroupBox("🔧 إعدادات عامة")
        general_form = QFormLayout(general_group)
        
        self.rtl_mode_checkbox = QCheckBox("تفعيل وضع اللغة العربية (RTL)")
        general_form.addRow(self.rtl_mode_checkbox)
        
        self.startup_backup_checkbox = QCheckBox("إنشاء نسخة احتياطية عند بدء التشغيل")
        general_form.addRow(self.startup_backup_checkbox)
        
        self.exit_backup_checkbox = QCheckBox("إنشاء نسخة احتياطية عند الإغلاق")
        general_form.addRow(self.exit_backup_checkbox)
        
        layout.addWidget(general_group)
        
        # معلومات النظام
        system_group = QGroupBox("ℹ️ معلومات النظام")
        system_layout = QVBoxLayout(system_group)
        
        system_info = QLabel("""
إصدار البرنامج: 1.0
قاعدة البيانات: SQLite
واجهة المستخدم: PyQt6
اللغة: Python 3.8+
        """.strip())
        system_info.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 15px;
                color: #495057;
                line-height: 1.5;
            }
        """)
        system_layout.addWidget(system_info)
        
        layout.addWidget(system_group)
        layout.addStretch()
        
        return widget
    
    def load_settings(self):
        """تحميل الإعدادات من قاعدة البيانات"""
        try:
            # معلومات العيادة
            clinic_name = self.db_manager.get_setting('clinic_name') or ""
            self.clinic_name_input.setText(clinic_name)
            
            clinic_address = self.db_manager.get_setting('clinic_address') or ""
            self.clinic_address_input.setPlainText(clinic_address)
            
            clinic_phone = self.db_manager.get_setting('clinic_phone') or ""
            self.clinic_phone_input.setText(clinic_phone)
            
            doctor_name = self.db_manager.get_setting('doctor_name') or ""
            self.doctor_name_input.setText(doctor_name)
            
            # الشعار
            logo_path = self.db_manager.get_setting('logo_path') or ""
            self.logo_path_input.setText(logo_path)
            self.update_logo_preview()
            
            # إعدادات الشبكة
            network_mode = self.db_manager.get_setting('network_mode') or 'standalone'
            if network_mode == 'standalone':
                self.network_mode_combo.setCurrentIndex(0)
            elif network_mode == 'master':
                self.network_mode_combo.setCurrentIndex(1)
            elif network_mode == 'client':
                self.network_mode_combo.setCurrentIndex(2)
            
            master_ip = self.db_manager.get_setting('master_ip') or ""
            self.master_ip_input.setText(master_ip)
            
            # النسخ الاحتياطي
            auto_backup = self.db_manager.get_setting('auto_backup') == 'true'
            self.auto_backup_checkbox.setChecked(auto_backup)
            
            backup_folder = self.db_manager.get_setting('backup_folder') or ""
            self.backup_folder_input.setText(backup_folder)
            
            # إعدادات عامة
            rtl_mode = self.db_manager.get_setting('rtl_mode') == 'true'
            self.rtl_mode_checkbox.setChecked(rtl_mode)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الإعدادات: {str(e)}")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # معلومات العيادة
            self.db_manager.set_setting('clinic_name', self.clinic_name_input.text())
            self.db_manager.set_setting('clinic_address', self.clinic_address_input.toPlainText())
            self.db_manager.set_setting('clinic_phone', self.clinic_phone_input.text())
            self.db_manager.set_setting('doctor_name', self.doctor_name_input.text())
            self.db_manager.set_setting('logo_path', self.logo_path_input.text())
            
            # إعدادات الشبكة
            network_modes = ['standalone', 'master', 'client']
            network_mode = network_modes[self.network_mode_combo.currentIndex()]
            self.db_manager.set_setting('network_mode', network_mode)
            self.db_manager.set_setting('master_ip', self.master_ip_input.text())
            
            # النسخ الاحتياطي
            auto_backup = 'true' if self.auto_backup_checkbox.isChecked() else 'false'
            self.db_manager.set_setting('auto_backup', auto_backup)
            self.db_manager.set_setting('backup_folder', self.backup_folder_input.text())
            
            # إعدادات عامة
            rtl_mode = 'true' if self.rtl_mode_checkbox.isChecked() else 'false'
            self.db_manager.set_setting('rtl_mode', rtl_mode)
            
            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
    
    def reset_settings(self):
        """استعادة الإعدادات الافتراضية"""
        reply = QMessageBox.question(
            self, "تأكيد الاستعادة",
            "هل أنت متأكد من استعادة الإعدادات الافتراضية؟\nسيتم فقدان جميع الإعدادات الحالية!",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # استعادة القيم الافتراضية
            self.clinic_name_input.setText("عيادة الدكتور أحمد محمد")
            self.clinic_address_input.setPlainText("شارع الملك فهد، الرياض")
            self.clinic_phone_input.setText("011-1234567")
            self.doctor_name_input.setText("د. أحمد محمد")
            self.logo_path_input.clear()
            self.network_mode_combo.setCurrentIndex(0)
            self.master_ip_input.clear()
            self.auto_backup_checkbox.setChecked(True)
            self.backup_folder_input.setText(os.path.join(os.path.expanduser("~"), "ClinicBackups"))
            self.rtl_mode_checkbox.setChecked(True)
            
            self.update_logo_preview()
    
    def browse_logo(self):
        """تصفح ملف الشعار"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self, "اختر شعار العيادة", "", 
            "Image files (*.png *.jpg *.jpeg *.bmp *.gif);;All files (*.*)"
        )
        
        if file_path:
            self.logo_path_input.setText(file_path)
            self.update_logo_preview()
    
    def remove_logo(self):
        """إزالة الشعار"""
        self.logo_path_input.clear()
        self.update_logo_preview()
    
    def update_logo_preview(self):
        """تحديث معاينة الشعار"""
        logo_path = self.logo_path_input.text()
        if logo_path and os.path.exists(logo_path):
            try:
                pixmap = QPixmap(logo_path)
                scaled_pixmap = pixmap.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                self.logo_preview.setPixmap(scaled_pixmap)
                self.logo_preview.setText("")
            except:
                self.logo_preview.clear()
                self.logo_preview.setText("خطأ في تحميل الصورة")
        else:
            self.logo_preview.clear()
            self.logo_preview.setText("لا يوجد شعار")
    
    def browse_backup_folder(self):
        """تصفح مجلد النسخ الاحتياطي"""
        folder_dialog = QFileDialog()
        folder_path = folder_dialog.getExistingDirectory(self, "اختر مجلد النسخ الاحتياطي")
        
        if folder_path:
            self.backup_folder_input.setText(folder_path)
    
    def create_backup_now(self):
        """إنشاء نسخة احتياطية فورية"""
        try:
            backup_path = self.db_manager.backup_database()
            QMessageBox.information(self, "نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
    
    def restore_backup(self):
        """استعادة من نسخة احتياطية"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self, "اختر ملف النسخة الاحتياطية", "", 
            "Database files (*.db);;All files (*.*)"
        )
        
        if file_path:
            reply = QMessageBox.question(
                self, "تأكيد الاستعادة",
                "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية!",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                QMessageBox.information(self, "تنبيه", "يرجى إعادة تشغيل البرنامج بعد استعادة النسخة الاحتياطية")
    
    def test_connection(self):
        """اختبار الاتصال بالشبكة"""
        network_mode = self.network_mode_combo.currentIndex()
        
        if network_mode == 0:  # Standalone
            QMessageBox.information(self, "وضع مستقل", "البرنامج يعمل في الوضع المستقل")
        elif network_mode == 1:  # Master
            QMessageBox.information(self, "جهاز رئيسي", "البرنامج يعمل كجهاز رئيسي")
        elif network_mode == 2:  # Client
            master_ip = self.master_ip_input.text()
            if master_ip:
                QMessageBox.information(self, "اختبار الاتصال", f"محاولة الاتصال بـ {master_ip}...")
            else:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال IP الجهاز الرئيسي")
