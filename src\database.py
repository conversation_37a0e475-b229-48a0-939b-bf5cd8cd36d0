import sqlite3
import os
from datetime import datetime

class Database:
    def __init__(self, db_path="clinic.db"):
        self.db_path = os.path.abspath(db_path)
        self.connection = None
        self.cursor = None

    def connect(self):
        """Create a database connection."""
        try:
            if self.connection is None:
                self.connection = sqlite3.connect(self.db_path)
                self.cursor = self.connection.cursor()
                self.cursor.execute("PRAGMA foreign_keys = ON")
                self.cursor.execute("PRAGMA encoding = 'UTF-8'")
            return True
        except sqlite3.Error as e:
            print(f"Error connecting to database: {e}")
            return False

    def ensure_connected(self):
        """Ensure database is connected before operations."""
        if self.connection is None:
            return self.connect()
        try:
            # Test the connection
            self.cursor.execute("SELECT 1")
            return True
        except sqlite3.Error:
            # Connection lost, try to reconnect
            self.close()
            return self.connect()

    def execute(self, query, params=()):
        """Execute a query with error handling and connection check."""
        if not self.ensure_connected():
            return False
        try:
            self.cursor.execute(query, params)
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error executing query: {e}")
            print(f"Query: {query}")
            print(f"Params: {params}")
            return False

    def fetchone(self, query, params=()):
        """Execute a query and fetch one result."""
        if not self.ensure_connected():
            return None
        try:
            self.cursor.execute(query, params)
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error executing query: {e}")
            return None

    def fetchall(self, query, params=()):
        """Execute a query and fetch all results."""
        if not self.ensure_connected():
            return []
        try:
            self.cursor.execute(query, params)
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error executing query: {e}")
            return []

    def close(self):
        """Close the database connection."""
        if self.connection:
            try:
                self.connection.close()
            except sqlite3.Error:
                pass
            finally:
                self.connection = None
                self.cursor = None

    def create_tables(self):
        """Create all necessary tables if they don't exist."""
        if not self.ensure_connected():
            return False

        try:
            # Users table (keeping it for compatibility)
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    password TEXT NOT NULL,
                    role TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Patients table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS patients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_number TEXT NOT NULL UNIQUE,
                    full_name TEXT NOT NULL,
                    phone_number TEXT,
                    gender TEXT,
                    age INTEGER,
                    address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Visits table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS visits (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    patient_id INTEGER,
                    visit_date TEXT NOT NULL,
                    weight REAL,
                    sugar REAL,
                    pressure TEXT,
                    notes TEXT,
                    treatment TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (patient_id) REFERENCES patients (id)
                )
            """)

            # Visit attachments table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS visit_attachments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    visit_id INTEGER,
                    file_path TEXT NOT NULL,
                    file_type TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (visit_id) REFERENCES visits (id)
                )
            """)

            # Settings table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            self.connection.commit()
            return True

        except sqlite3.Error as e:
            print(f"Error creating tables: {e}")
            return False

    def _insert_default_users(self):
        """Insert default users (doctor and secretary)."""
        default_users = [
            ("doctor", "doctor123", "doctor"),
            ("secretary", "secretary123", "secretary")
        ]
        for username, password, role in default_users:
            try:
                self.cursor.execute(
                    "INSERT OR IGNORE INTO users (username, password, role) VALUES (?, ?, ?)",
                    (username, password, role)
                )
            except sqlite3.Error as e:
                print(f"Error inserting default user {username}: {e}")

    def _insert_default_settings(self):
        """Insert default settings."""
        default_settings = {
            "clinic_name": "عيادة الشفاء",
            "clinic_address": "شارع الملك فهد",
            "doctor_name": "د. محمد أحمد",
            "clinic_phone": "0123456789",
            "clinic_logo": "",  # Path to logo file
            "backup_folder": os.path.join(os.path.expanduser("~"), "ClinicBackups"),
            "network_mode": "standalone",  # standalone, server, or client
            "server_ip": "",
            "auto_backup": "true",
            "rtl_mode": "true"
        }
        
        for key, value in default_settings.items():
            try:
                self.cursor.execute(
                    "INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)",
                    (key, value)
                )
            except sqlite3.Error as e:
                print(f"Error inserting default setting {key}: {e}")

    def backup_database(self, backup_folder=None):
        """Create a backup of the database."""
        if not backup_folder:
            self.cursor.execute("SELECT value FROM settings WHERE key = 'backup_folder'")
            result = self.cursor.fetchone()
            if result:
                backup_folder = result[0]
            else:
                backup_folder = os.path.join(os.path.expanduser("~"), "ClinicBackups")

        # Create backup folder if it doesn't exist
        os.makedirs(backup_folder, exist_ok=True)

        # Create backup filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_folder, f"clinic_backup_{timestamp}.db")

        try:
            # Close existing connection
            self.close()

            # Copy database file
            with open(self.db_path, 'rb') as source:
                with open(backup_path, 'wb') as dest:
                    dest.write(source.read())

            # Reconnect to database
            self.connect()
            return True, backup_path

        except Exception as e:
            print(f"Error creating backup: {e}")
            self.connect()  # Ensure we're reconnected
            return False, str(e)

    def get_setting(self, key):
        """Get a setting value by key."""
        try:
            self.cursor.execute("SELECT value FROM settings WHERE key = ?", (key,))
            result = self.cursor.fetchone()
            return result[0] if result else None
        except sqlite3.Error as e:
            print(f"Error getting setting {key}: {e}")
            return None

    def update_setting(self, key, value):
        """Update a setting value."""
        try:
            self.cursor.execute(
                """
                INSERT OR REPLACE INTO settings (key, value, updated_at)
                VALUES (?, ?, CURRENT_TIMESTAMP)
                """,
                (key, value)
            )
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error updating setting {key}: {e}")
            return False

    def initialize_database(self):
        """Initialize database tables and default data."""
        try:
            if not self.ensure_connected():
                return False
                
            # Create tables
            tables = [
                # Users table
                """
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    role TEXT NOT NULL CHECK (role IN ('doctor', 'secretary')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                # Patients table
                """
                CREATE TABLE IF NOT EXISTS patients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_number TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    birth_date DATE,
                    gender TEXT CHECK (gender IN ('M', 'F')),
                    phone TEXT,
                    address TEXT,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                # Visits table
                """
                CREATE TABLE IF NOT EXISTS visits (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    patient_id INTEGER NOT NULL,
                    visit_date TIMESTAMP NOT NULL,
                    weight REAL,
                    blood_sugar REAL,
                    blood_pressure TEXT,
                    diagnosis TEXT,
                    treatment TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE
                )
                """,
                # Medications table
                """
                CREATE TABLE IF NOT EXISTS medications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    medication TEXT UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                # Attachments table
                """
                CREATE TABLE IF NOT EXISTS attachments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    visit_id INTEGER NOT NULL,
                    file_name TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (visit_id) REFERENCES visits(id) ON DELETE CASCADE
                )
                """,
                # Settings table
                """
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    clinic_name TEXT,
                    doctor_name TEXT,
                    address TEXT,
                    phone TEXT,
                    logo_path TEXT,
                    backup_path TEXT,
                    lan_enabled INTEGER DEFAULT 0,
                    lan_ip TEXT,
                    lan_port INTEGER DEFAULT 5000,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
            ]
            
            for table in tables:
                self.cursor.execute(table)
                
            # Add default users if they don't exist
            default_users = [
                ('doctor', 'doctor123', 'doctor'),
                ('secretary', 'secretary123', 'secretary')
            ]
            
            self.cursor.execute("SELECT COUNT(*) FROM users")
            if self.cursor.fetchone()[0] == 0:
                for username, password, role in default_users:
                    self.cursor.execute(
                        "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                        (username, password, role)
                    )
                    
            # Add default settings if they don't exist
            self.cursor.execute("SELECT COUNT(*) FROM settings")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.execute(
                    "INSERT INTO settings (clinic_name, doctor_name) VALUES (?, ?)",
                    ("عيادتي", "د. الطبيب")
                )
                
            self.connection.commit()
            return True
            
        except sqlite3.Error as e:
            print(f"Error initializing database: {e}")
            return False

# Create an instance for global use
db = Database()
