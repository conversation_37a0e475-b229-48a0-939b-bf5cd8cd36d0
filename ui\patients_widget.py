from PyQt6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QMessageBox, QDialog, QFormLayout, QComboBox,
                            QSpinBox, QTextEdit, QHeaderView, QFrame, QSplitter)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from models.patient import Patient

class PatientDialog(QDialog):
    """نافذة إضافة/تعديل مريض"""
    
    def __init__(self, patient_model, patient_data=None, parent=None):
        super().__init__(parent)
        self.patient_model = patient_model
        self.patient_data = patient_data
        self.is_edit_mode = patient_data is not None
        self.init_ui()
        
        if self.is_edit_mode:
            self.load_patient_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل مريض" if self.is_edit_mode else "إضافة مريض جديد"
        self.setWindowTitle(title)
        self.setFixedSize(400, 500)
        
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # رقم الملف
        self.file_number_input = QLineEdit()
        if not self.is_edit_mode:
            # الحصول على رقم الملف التالي
            next_number = self.patient_model.get_next_file_number()
            self.file_number_input.setText(next_number)
        form_layout.addRow("رقم الملف:", self.file_number_input)
        
        # الاسم الكامل
        self.full_name_input = QLineEdit()
        form_layout.addRow("الاسم الكامل:", self.full_name_input)
        
        # رقم الهاتف
        self.phone_input = QLineEdit()
        form_layout.addRow("رقم الهاتف:", self.phone_input)
        
        # الجنس
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["", "ذكر", "أنثى"])
        form_layout.addRow("الجنس:", self.gender_combo)
        
        # العمر
        self.age_input = QSpinBox()
        self.age_input.setRange(0, 150)
        self.age_input.setValue(0)
        form_layout.addRow("العمر:", self.age_input)
        
        # العنوان
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        form_layout.addRow("العنوان:", self.address_input)
        
        layout.addLayout(form_layout)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.save_patient)
        buttons_layout.addWidget(self.save_button)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLineEdit, QComboBox, QSpinBox, QTextEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QTextEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
    
    def load_patient_data(self):
        """تحميل بيانات المريض للتعديل"""
        if self.patient_data:
            self.file_number_input.setText(str(self.patient_data['file_number']))
            self.file_number_input.setReadOnly(True)  # منع تعديل رقم الملف
            
            self.full_name_input.setText(self.patient_data['full_name'] or "")
            self.phone_input.setText(self.patient_data['phone'] or "")
            
            if self.patient_data['gender']:
                gender_text = "ذكر" if self.patient_data['gender'] == 'male' else "أنثى"
                self.gender_combo.setCurrentText(gender_text)
            
            if self.patient_data['age']:
                self.age_input.setValue(self.patient_data['age'])
            
            self.address_input.setPlainText(self.patient_data['address'] or "")
    
    def save_patient(self):
        """حفظ بيانات المريض"""
        # التحقق من صحة البيانات
        file_number = self.file_number_input.text().strip()
        full_name = self.full_name_input.text().strip()
        
        if not file_number:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الملف")
            return
        
        if not full_name:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال الاسم الكامل")
            return
        
        # جمع البيانات
        phone = self.phone_input.text().strip() or None
        
        gender = None
        if self.gender_combo.currentText() == "ذكر":
            gender = "male"
        elif self.gender_combo.currentText() == "أنثى":
            gender = "female"
        
        age = self.age_input.value() if self.age_input.value() > 0 else None
        address = self.address_input.toPlainText().strip() or None
        
        try:
            if self.is_edit_mode:
                # تحديث المريض
                success = self.patient_model.update_patient(
                    self.patient_data['id'],
                    full_name=full_name,
                    phone=phone,
                    gender=gender,
                    age=age,
                    address=address
                )
                if success:
                    QMessageBox.information(self, "نجح", "تم تحديث بيانات المريض بنجاح")
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تحديث بيانات المريض")
            else:
                # إضافة مريض جديد
                patient_id = self.patient_model.add_patient(
                    file_number, full_name, phone, gender, age, address
                )
                if patient_id:
                    QMessageBox.information(self, "نجح", "تم إضافة المريض بنجاح")
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إضافة المريض")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

class PatientsWidget(QWidget):
    """ويدجت إدارة المرضى"""
    
    patient_selected = pyqtSignal(dict)  # إشارة اختيار مريض
    
    def __init__(self, patient_model, parent=None):
        super().__init__(parent)
        self.patient_model = patient_model
        self.current_patients = []
        self.init_ui()
        self.load_patients()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط العنوان والبحث
        header_layout = QHBoxLayout()
        
        title_label = QLabel("إدارة المرضى")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # مربع البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في المرضى...")
        self.search_input.setFixedWidth(250)
        self.search_input.textChanged.connect(self.search_patients)
        header_layout.addWidget(self.search_input)
        
        # زر إضافة مريض
        add_button = QPushButton("إضافة مريض جديد")
        add_button.clicked.connect(self.add_patient)
        header_layout.addWidget(add_button)
        
        layout.addLayout(header_layout)
        
        # جدول المرضى
        self.patients_table = QTableWidget()
        self.patients_table.setColumnCount(6)
        self.patients_table.setHorizontalHeaderLabels([
            "رقم الملف", "الاسم الكامل", "الهاتف", "الجنس", "العمر", "العنوان"
        ])
        
        # تعديل عرض الأعمدة
        header = self.patients_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)
        
        # إعداد الجدول
        self.patients_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.patients_table.setAlternatingRowColors(True)
        self.patients_table.doubleClicked.connect(self.edit_patient)
        self.patients_table.itemSelectionChanged.connect(self.on_patient_selected)
        
        layout.addWidget(self.patients_table)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        edit_button = QPushButton("تعديل")
        edit_button.clicked.connect(self.edit_patient)
        buttons_layout.addWidget(edit_button)
        
        delete_button = QPushButton("حذف")
        delete_button.clicked.connect(self.delete_patient)
        buttons_layout.addWidget(delete_button)
        
        view_visits_button = QPushButton("عرض الزيارات")
        view_visits_button.clicked.connect(self.view_patient_visits)
        buttons_layout.addWidget(view_visits_button)
        
        buttons_layout.addStretch()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_patients)
        buttons_layout.addWidget(refresh_button)
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
    
    def load_patients(self):
        """تحميل قائمة المرضى"""
        try:
            self.current_patients = self.patient_model.search_patients()
            self.update_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المرضى: {str(e)}")
    
    def update_table(self):
        """تحديث جدول المرضى"""
        self.patients_table.setRowCount(len(self.current_patients))
        
        for row, patient in enumerate(self.current_patients):
            self.patients_table.setItem(row, 0, QTableWidgetItem(str(patient['file_number'])))
            self.patients_table.setItem(row, 1, QTableWidgetItem(patient['full_name']))
            self.patients_table.setItem(row, 2, QTableWidgetItem(patient['phone'] or ""))
            
            gender_text = ""
            if patient['gender'] == 'male':
                gender_text = "ذكر"
            elif patient['gender'] == 'female':
                gender_text = "أنثى"
            self.patients_table.setItem(row, 3, QTableWidgetItem(gender_text))
            
            self.patients_table.setItem(row, 4, QTableWidgetItem(str(patient['age']) if patient['age'] else ""))
            self.patients_table.setItem(row, 5, QTableWidgetItem(patient['address'] or ""))
    
    def search_patients(self):
        """البحث في المرضى"""
        search_term = self.search_input.text().strip()
        try:
            self.current_patients = self.patient_model.search_patients(search_term)
            self.update_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث: {str(e)}")
    
    def add_patient(self):
        """إضافة مريض جديد"""
        dialog = PatientDialog(self.patient_model, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_patients()
    
    def edit_patient(self):
        """تعديل مريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            patient_data = self.current_patients[current_row]
            dialog = PatientDialog(self.patient_model, patient_data, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_patients()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض للتعديل")
    
    def delete_patient(self):
        """حذف مريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            patient_data = self.current_patients[current_row]
            
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف المريض:\n{patient_data['full_name']}؟\n\nسيتم حذف جميع زياراته أيضاً!",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    success = self.patient_model.delete_patient(patient_data['id'])
                    if success:
                        QMessageBox.information(self, "نجح", "تم حذف المريض بنجاح")
                        self.load_patients()
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف المريض")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض للحذف")
    
    def view_patient_visits(self):
        """عرض زيارات المريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            patient_data = self.current_patients[current_row]
            self.patient_selected.emit(patient_data)
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض لعرض زياراته")
    
    def on_patient_selected(self):
        """التعامل مع اختيار مريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            patient_data = self.current_patients[current_row]
            # يمكن إضافة منطق إضافي هنا
            pass
