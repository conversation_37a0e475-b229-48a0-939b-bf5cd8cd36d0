from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QMessageBox, QDialog, QComboBox,
                            QSpinBox, QTextEdit, QHeaderView, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

class PatientDialog(QDialog):
    """نافذة إضافة/تعديل مريض"""
    
    def __init__(self, patient_model, patient_data=None, parent=None):
        super().__init__(parent)
        self.patient_model = patient_model
        self.patient_data = patient_data
        self.is_edit_mode = patient_data is not None
        self.init_ui()
        
        if self.is_edit_mode:
            self.load_patient_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل مريض" if self.is_edit_mode else "إضافة مريض جديد"
        self.setWindowTitle(title)
        self.setFixedSize(450, 600)

        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # نموذج البيانات
        form_widget = QWidget()
        form_layout = QVBoxLayout(form_widget)
        form_layout.setSpacing(12)

        # رقم الملف
        file_number_layout = QVBoxLayout()
        file_number_label = QLabel("رقم الملف:")
        file_number_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        file_number_layout.addWidget(file_number_label)

        self.file_number_input = QLineEdit()
        self.file_number_input.setFixedHeight(35)
        if not self.is_edit_mode:
            next_number = self.patient_model.get_next_file_number()
            self.file_number_input.setText(next_number)
        file_number_layout.addWidget(self.file_number_input)
        form_layout.addLayout(file_number_layout)

        # الاسم الكامل
        name_layout = QVBoxLayout()
        name_label = QLabel("الاسم الكامل:")
        name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        name_layout.addWidget(name_label)

        self.full_name_input = QLineEdit()
        self.full_name_input.setFixedHeight(35)
        name_layout.addWidget(self.full_name_input)
        form_layout.addLayout(name_layout)

        # رقم الهاتف
        phone_layout = QVBoxLayout()
        phone_label = QLabel("رقم الهاتف:")
        phone_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        phone_layout.addWidget(phone_label)

        self.phone_input = QLineEdit()
        self.phone_input.setFixedHeight(35)
        phone_layout.addWidget(self.phone_input)
        form_layout.addLayout(phone_layout)

        # الجنس والعمر في صف واحد
        gender_age_layout = QHBoxLayout()

        # الجنس
        gender_layout = QVBoxLayout()
        gender_label = QLabel("الجنس:")
        gender_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        gender_layout.addWidget(gender_label)

        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["", "ذكر", "أنثى"])
        self.gender_combo.setFixedHeight(35)
        gender_layout.addWidget(self.gender_combo)
        gender_age_layout.addLayout(gender_layout)

        # العمر
        age_layout = QVBoxLayout()
        age_label = QLabel("العمر:")
        age_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        age_layout.addWidget(age_label)

        self.age_input = QSpinBox()
        self.age_input.setRange(0, 150)
        self.age_input.setValue(0)
        self.age_input.setFixedHeight(35)
        age_layout.addWidget(self.age_input)
        gender_age_layout.addLayout(age_layout)

        form_layout.addLayout(gender_age_layout)

        # العنوان
        address_layout = QVBoxLayout()
        address_label = QLabel("العنوان:")
        address_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        address_layout.addWidget(address_label)

        self.address_input = QTextEdit()
        self.address_input.setFixedHeight(80)
        address_layout.addWidget(self.address_input)
        form_layout.addLayout(address_layout)

        layout.addWidget(form_widget)
        
        # مساحة فارغة
        layout.addStretch()

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setFixedHeight(40)
        self.save_button.setFixedWidth(120)
        self.save_button.clicked.connect(self.save_patient)
        buttons_layout.addWidget(self.save_button)

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(40)
        cancel_button.setFixedWidth(120)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

        # تطبيق الستايل المحسن
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border-radius: 8px;
            }
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                margin-bottom: 5px;
            }
            QLineEdit, QComboBox, QSpinBox, QTextEdit {
                padding: 10px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
                selection-background-color: #3498db;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QTextEdit:focus {
                border-color: #3498db;
                outline: none;
            }
            QLineEdit:hover, QComboBox:hover, QSpinBox:hover, QTextEdit:hover {
                border-color: #bdc3c7;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #21618c;
                transform: translateY(0px);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
                margin-right: 5px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 20px;
                border: none;
                background-color: #ecf0f1;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #bdc3c7;
            }
        """)
    
    def load_patient_data(self):
        """تحميل بيانات المريض للتعديل"""
        if self.patient_data:
            self.file_number_input.setText(str(self.patient_data['file_number']))
            self.file_number_input.setReadOnly(True)  # منع تعديل رقم الملف
            
            self.full_name_input.setText(self.patient_data['full_name'] or "")
            self.phone_input.setText(self.patient_data['phone'] or "")
            
            if self.patient_data['gender']:
                gender_text = "ذكر" if self.patient_data['gender'] == 'male' else "أنثى"
                self.gender_combo.setCurrentText(gender_text)
            
            if self.patient_data['age']:
                self.age_input.setValue(self.patient_data['age'])
            
            self.address_input.setPlainText(self.patient_data['address'] or "")
    
    def save_patient(self):
        """حفظ بيانات المريض"""
        # التحقق من صحة البيانات
        file_number = self.file_number_input.text().strip()
        full_name = self.full_name_input.text().strip()
        
        if not file_number:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الملف")
            return
        
        if not full_name:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال الاسم الكامل")
            return
        
        # جمع البيانات
        phone = self.phone_input.text().strip() or None
        
        gender = None
        if self.gender_combo.currentText() == "ذكر":
            gender = "male"
        elif self.gender_combo.currentText() == "أنثى":
            gender = "female"
        
        age = self.age_input.value() if self.age_input.value() > 0 else None
        address = self.address_input.toPlainText().strip() or None
        
        try:
            if self.is_edit_mode:
                # تحديث المريض
                success = self.patient_model.update_patient(
                    self.patient_data['id'],
                    full_name=full_name,
                    phone=phone,
                    gender=gender,
                    age=age,
                    address=address
                )
                if success:
                    QMessageBox.information(self, "نجح", "تم تحديث بيانات المريض بنجاح")
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تحديث بيانات المريض")
            else:
                # إضافة مريض جديد
                patient_id = self.patient_model.add_patient(
                    file_number, full_name, phone, gender, age, address
                )
                if patient_id:
                    QMessageBox.information(self, "نجح", "تم إضافة المريض بنجاح")
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إضافة المريض")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

class PatientsWidget(QWidget):
    """ويدجت إدارة المرضى"""
    
    patient_selected = pyqtSignal(dict)  # إشارة اختيار مريض
    
    def __init__(self, patient_model, parent=None):
        super().__init__(parent)
        self.patient_model = patient_model
        self.current_patients = []
        self.init_ui()
        self.load_patients()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # شريط العنوان والبحث
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
        """)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setSpacing(15)

        # العنوان
        title_label = QLabel("👥 إدارة المرضى")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; border: none; padding: 0;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # مربع البحث
        search_container = QFrame()
        search_container.setStyleSheet("QFrame { border: none; }")
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)

        search_label = QLabel("🔍")
        search_label.setStyleSheet("color: #7f8c8d; border: none; padding: 0; margin-right: 5px;")
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في المرضى (الاسم، رقم الملف، الهاتف)...")
        self.search_input.setFixedWidth(300)
        self.search_input.setFixedHeight(35)
        self.search_input.textChanged.connect(self.search_patients)
        search_layout.addWidget(self.search_input)

        header_layout.addWidget(search_container)

        # زر إضافة مريض
        add_button = QPushButton("➕ إضافة مريض جديد")
        add_button.setFixedHeight(35)
        add_button.setFixedWidth(150)
        add_button.clicked.connect(self.add_patient)
        header_layout.addWidget(add_button)

        layout.addWidget(header_frame)
        
        # جدول المرضى
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(15, 15, 15, 15)

        # عنوان الجدول
        table_title = QLabel("قائمة المرضى")
        table_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        table_title.setStyleSheet("color: #2c3e50; border: none; padding: 0; margin-bottom: 10px;")
        table_layout.addWidget(table_title)

        self.patients_table = QTableWidget()
        self.patients_table.setColumnCount(6)
        self.patients_table.setHorizontalHeaderLabels([
            "رقم الملف", "الاسم الكامل", "الهاتف", "الجنس", "العمر", "العنوان"
        ])

        # تعديل عرض الأعمدة
        header = self.patients_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)

        # إعداد الجدول
        self.patients_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.patients_table.setAlternatingRowColors(True)
        self.patients_table.doubleClicked.connect(self.edit_patient)
        self.patients_table.itemSelectionChanged.connect(self.on_patient_selected)
        self.patients_table.setMinimumHeight(400)

        table_layout.addWidget(self.patients_table)
        layout.addWidget(table_frame)
        
        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                padding: 10px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        edit_button = QPushButton("✏️ تعديل")
        edit_button.setFixedHeight(35)
        edit_button.clicked.connect(self.edit_patient)
        buttons_layout.addWidget(edit_button)

        delete_button = QPushButton("🗑️ حذف")
        delete_button.setFixedHeight(35)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        delete_button.clicked.connect(self.delete_patient)
        buttons_layout.addWidget(delete_button)

        view_visits_button = QPushButton("📋 عرض الزيارات")
        view_visits_button.setFixedHeight(35)
        view_visits_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        view_visits_button.clicked.connect(self.view_patient_visits)
        buttons_layout.addWidget(view_visits_button)

        buttons_layout.addStretch()

        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.setFixedHeight(35)
        refresh_button.clicked.connect(self.load_patients)
        buttons_layout.addWidget(refresh_button)

        layout.addWidget(buttons_frame)
        
        # تطبيق الستايل المحسن
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLineEdit {
                padding: 10px 15px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
                selection-background-color: #3498db;
            }
            QLineEdit:focus {
                border-color: #3498db;
                outline: none;
            }
            QLineEdit:hover {
                border-color: #bdc3c7;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #21618c;
                transform: translateY(0px);
            }
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: none;
                border-radius: 6px;
                selection-background-color: #3498db;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #ecf0f1;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 13px;
            }
            QHeaderView::section:hover {
                background-color: #34495e;
            }
            QScrollBar:vertical {
                background-color: #ecf0f1;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #bdc3c7;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #95a5a6;
            }
        """)
    
    def load_patients(self):
        """تحميل قائمة المرضى"""
        try:
            self.current_patients = self.patient_model.search_patients()
            self.update_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المرضى: {str(e)}")
    
    def update_table(self):
        """تحديث جدول المرضى"""
        self.patients_table.setRowCount(len(self.current_patients))
        
        for row, patient in enumerate(self.current_patients):
            self.patients_table.setItem(row, 0, QTableWidgetItem(str(patient['file_number'])))
            self.patients_table.setItem(row, 1, QTableWidgetItem(patient['full_name']))
            self.patients_table.setItem(row, 2, QTableWidgetItem(patient['phone'] or ""))
            
            gender_text = ""
            if patient['gender'] == 'male':
                gender_text = "ذكر"
            elif patient['gender'] == 'female':
                gender_text = "أنثى"
            self.patients_table.setItem(row, 3, QTableWidgetItem(gender_text))
            
            self.patients_table.setItem(row, 4, QTableWidgetItem(str(patient['age']) if patient['age'] else ""))
            self.patients_table.setItem(row, 5, QTableWidgetItem(patient['address'] or ""))
    
    def search_patients(self):
        """البحث في المرضى"""
        search_term = self.search_input.text().strip()
        try:
            self.current_patients = self.patient_model.search_patients(search_term)
            self.update_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث: {str(e)}")
    
    def add_patient(self):
        """إضافة مريض جديد"""
        dialog = PatientDialog(self.patient_model, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_patients()
    
    def edit_patient(self):
        """تعديل مريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            patient_data = self.current_patients[current_row]
            dialog = PatientDialog(self.patient_model, patient_data, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_patients()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض للتعديل")
    
    def delete_patient(self):
        """حذف مريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            patient_data = self.current_patients[current_row]
            
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف المريض:\n{patient_data['full_name']}؟\n\nسيتم حذف جميع زياراته أيضاً!",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    success = self.patient_model.delete_patient(patient_data['id'])
                    if success:
                        QMessageBox.information(self, "نجح", "تم حذف المريض بنجاح")
                        self.load_patients()
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف المريض")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض للحذف")
    
    def view_patient_visits(self):
        """عرض زيارات المريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            patient_data = self.current_patients[current_row]
            self.patient_selected.emit(patient_data)
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض لعرض زياراته")
    
    def on_patient_selected(self):
        """التعامل مع اختيار مريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            # يمكن إضافة منطق إضافي هنا عند اختيار مريض
            pass
