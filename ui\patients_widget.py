from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QMessageBox, QDialog, QComboBox,
                            QSpinBox, QTextEdit, QHeaderView, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from styles.style_manager import StyleManager

class PatientDialog(QDialog):
    """نافذة إضافة/تعديل مريض"""
    
    def __init__(self, patient_model, patient_data=None, parent=None):
        super().__init__(parent)
        self.patient_model = patient_model
        self.patient_data = patient_data
        self.is_edit_mode = patient_data is not None
        self.style_manager = StyleManager()
        self.init_ui()
        
        if self.is_edit_mode:
            self.load_patient_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم العصرية"""
        title = "تعديل مريض" if self.is_edit_mode else "إضافة مريض جديد"
        self.setWindowTitle(title)
        self.setMinimumSize(600, 750)
        self.resize(600, 750)
        
        # تطبيق الأنماط العصرية
        combined_style = (
            self.style_manager.get_main_style() +
            self.style_manager.get_button_style() +
            self.style_manager.get_input_style() +
            self.style_manager.get_modern_card_style()
        )
        self.setStyleSheet(combined_style)

        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # عنوان النافذة في بطاقة
        header_frame = QFrame()
        header_frame.setObjectName("cardFrame")
        header_layout = QVBoxLayout(header_frame)
        
        title_label = QLabel(title)
        title_label.setObjectName("mainTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)
        layout.addWidget(header_frame)

        # نموذج البيانات في بطاقة
        form_frame = QFrame()
        form_frame.setObjectName("cardFrame")
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(20)
        form_layout.setContentsMargins(25, 25, 25, 25)

        # الصف الأول: رقم الملف والاسم الكامل
        first_row = QHBoxLayout()
        first_row.setSpacing(20)

        # رقم الملف
        file_number_layout = QVBoxLayout()
        file_number_label = QLabel("📋 رقم الملف:")
        file_number_label.setObjectName("sectionTitle")
        file_number_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px; margin-bottom: 5px;")
        file_number_layout.addWidget(file_number_label)

        self.file_number_input = QLineEdit()
        self.file_number_input.setMinimumHeight(40)
        self.file_number_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        if not self.is_edit_mode:
            next_number = self.patient_model.get_next_file_number()
            self.file_number_input.setText(next_number)
        file_number_layout.addWidget(self.file_number_input)
        first_row.addLayout(file_number_layout)

        # الاسم الكامل
        name_layout = QVBoxLayout()
        name_label = QLabel("👤 الاسم الكامل:")
        name_label.setObjectName("sectionTitle")
        name_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px; margin-bottom: 5px;")
        name_layout.addWidget(name_label)

        self.full_name_input = QLineEdit()
        self.full_name_input.setMinimumHeight(40)
        self.full_name_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        name_layout.addWidget(self.full_name_input)
        first_row.addLayout(name_layout)

        form_layout.addLayout(first_row)

        # الصف الثاني: رقم الهاتف
        phone_layout = QVBoxLayout()
        phone_label = QLabel("📞 رقم الهاتف:")
        phone_label.setObjectName("sectionTitle")
        phone_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px; margin-bottom: 5px;")
        phone_layout.addWidget(phone_label)

        self.phone_input = QLineEdit()
        self.phone_input.setMinimumHeight(40)
        self.phone_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        phone_layout.addWidget(self.phone_input)
        form_layout.addLayout(phone_layout)

        # الصف الثالث: الجنس والعمر
        gender_age_layout = QHBoxLayout()
        gender_age_layout.setSpacing(20)

        # الجنس
        gender_layout = QVBoxLayout()
        gender_label = QLabel("⚥ الجنس:")
        gender_label.setObjectName("sectionTitle")
        gender_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px; margin-bottom: 5px;")
        gender_layout.addWidget(gender_label)

        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["", "ذكر", "أنثى"])
        self.gender_combo.setMinimumHeight(40)
        self.gender_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
                min-width: 120px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
                margin-right: 5px;
            }
        """)
        gender_layout.addWidget(self.gender_combo)
        gender_age_layout.addLayout(gender_layout)

        # العمر
        age_layout = QVBoxLayout()
        age_label = QLabel("🎂 العمر:")
        age_label.setObjectName("sectionTitle")
        age_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px; margin-bottom: 5px;")
        age_layout.addWidget(age_label)

        self.age_input = QSpinBox()
        self.age_input.setRange(0, 150)
        self.age_input.setValue(0)
        self.age_input.setMinimumHeight(40)
        self.age_input.setStyleSheet("""
            QSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
                min-width: 100px;
            }
            QSpinBox:focus {
                border-color: #3498db;
            }
        """)
        age_layout.addWidget(self.age_input)
        gender_age_layout.addLayout(age_layout)

        form_layout.addLayout(gender_age_layout)

        # العنوان
        address_layout = QVBoxLayout()
        address_label = QLabel("🏠 العنوان:")
        address_label.setObjectName("sectionTitle")
        address_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px; margin-bottom: 5px;")
        address_layout.addWidget(address_label)

        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(100)
        self.address_input.setMinimumHeight(80)
        self.address_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        address_layout.addWidget(self.address_input)
        form_layout.addLayout(address_layout)

        # صورة المريض
        photo_layout = QVBoxLayout()
        photo_label = QLabel("📷 صورة المريض:")
        photo_label.setObjectName("sectionTitle")
        photo_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px; margin-bottom: 10px;")
        photo_layout.addWidget(photo_label)

        # منطقة الصورة مع الأزرار
        photo_container = QHBoxLayout()

        # عرض الصورة
        self.photo_label = QLabel("لا توجد صورة")
        self.photo_label.setFixedSize(120, 120)
        self.photo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.photo_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: #7f8c8d;
                font-size: 12px;
            }
        """)
        self.photo_label.setScaledContents(True)
        photo_container.addWidget(self.photo_label)

        # أزرار الصورة
        photo_buttons_layout = QVBoxLayout()

        # زر اختيار صورة
        self.select_photo_button = QPushButton("📷 اختيار صورة")
        self.select_photo_button.setMinimumHeight(35)
        self.select_photo_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.select_photo_button.clicked.connect(self.select_photo)
        photo_buttons_layout.addWidget(self.select_photo_button)

        # زر فتح الصورة
        self.open_photo_button = QPushButton("👁️ فتح الصورة")
        self.open_photo_button.setMinimumHeight(35)
        self.open_photo_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.open_photo_button.clicked.connect(self.open_photo)
        self.open_photo_button.setEnabled(False)  # معطل في البداية
        photo_buttons_layout.addWidget(self.open_photo_button)

        # زر حذف الصورة
        self.remove_photo_button = QPushButton("🗑️ حذف الصورة")
        self.remove_photo_button.setMinimumHeight(35)
        self.remove_photo_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.remove_photo_button.clicked.connect(self.remove_photo)
        self.remove_photo_button.setEnabled(False)  # معطل في البداية
        photo_buttons_layout.addWidget(self.remove_photo_button)

        photo_buttons_layout.addStretch()
        photo_container.addLayout(photo_buttons_layout)

        photo_layout.addLayout(photo_container)
        form_layout.addLayout(photo_layout)

        # متغير لحفظ مسار الصورة
        self.photo_path = None

        layout.addWidget(form_frame)
        
        # مساحة فارغة
        layout.addStretch()

        # أزرار التحكم العصرية
        buttons_frame = QFrame()
        buttons_frame.setObjectName("cardFrame")
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)

        self.save_button = QPushButton("💾  حفظ")
        self.save_button.setObjectName("successButton")
        self.save_button.setMinimumHeight(45)
        self.save_button.setMinimumWidth(140)
        self.save_button.clicked.connect(self.save_patient)
        buttons_layout.addWidget(self.save_button)

        cancel_button = QPushButton("❌  إلغاء")
        cancel_button.setObjectName("secondaryButton")
        cancel_button.setMinimumHeight(45)
        cancel_button.setMinimumWidth(140)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)

        layout.addWidget(buttons_frame)

        # تطبيق الستايل المحسن
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border-radius: 8px;
            }
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                margin-bottom: 5px;
            }
            QLineEdit, QComboBox, QSpinBox, QTextEdit {
                padding: 10px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
                selection-background-color: #3498db;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QTextEdit:focus {
                border-color: #3498db;
                outline: none;
            }
            QLineEdit:hover, QComboBox:hover, QSpinBox:hover, QTextEdit:hover {
                border-color: #bdc3c7;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #21618c;
                transform: translateY(0px);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
                margin-right: 5px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 20px;
                border: none;
                background-color: #ecf0f1;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #bdc3c7;
            }
        """)
    
    def load_patient_data(self):
        """تحميل بيانات المريض للتعديل"""
        if self.patient_data:
            self.file_number_input.setText(str(self.patient_data['file_number']))
            self.file_number_input.setReadOnly(True)  # منع تعديل رقم الملف
            
            self.full_name_input.setText(self.patient_data['full_name'] or "")
            self.phone_input.setText(self.patient_data['phone'] or "")
            
            if self.patient_data['gender']:
                gender_text = "ذكر" if self.patient_data['gender'] == 'male' else "أنثى"
                self.gender_combo.setCurrentText(gender_text)
            
            if self.patient_data['age']:
                self.age_input.setValue(self.patient_data['age'])
            
            self.address_input.setPlainText(self.patient_data['address'] or "")

            # تحميل الصورة إذا كانت موجودة
            if self.patient_data.get('photo_path'):
                self.load_patient_photo(self.patient_data['photo_path'])

    def load_patient_photo(self, photo_path):
        """تحميل صورة المريض"""
        try:
            import os
            from PyQt6.QtGui import QPixmap

            if photo_path and os.path.exists(photo_path):
                pixmap = QPixmap(photo_path)
                if not pixmap.isNull():
                    # تصغير الصورة للعرض
                    scaled_pixmap = pixmap.scaled(
                        120, 120,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )

                    self.photo_label.setPixmap(scaled_pixmap)
                    self.photo_path = photo_path

                    # تفعيل أزرار الصورة
                    self.open_photo_button.setEnabled(True)
                    self.remove_photo_button.setEnabled(True)

                    # تحديث نص التسمية
                    self.photo_label.setText("")

        except Exception as e:
            print(f"خطأ في تحميل صورة المريض: {e}")
    
    def save_patient(self):
        """حفظ بيانات المريض"""
        # التحقق من صحة البيانات
        file_number = self.file_number_input.text().strip()
        full_name = self.full_name_input.text().strip()
        
        if not file_number:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الملف")
            return
        
        if not full_name:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال الاسم الكامل")
            return
        
        # جمع البيانات
        phone = self.phone_input.text().strip() or None
        
        gender = None
        if self.gender_combo.currentText() == "ذكر":
            gender = "male"
        elif self.gender_combo.currentText() == "أنثى":
            gender = "female"
        
        age = self.age_input.value() if self.age_input.value() > 0 else None
        address = self.address_input.toPlainText().strip() or None
        
        try:
            if self.is_edit_mode:
                # تحديث المريض
                success = self.patient_model.update_patient(
                    self.patient_data['id'],
                    full_name=full_name,
                    phone=phone,
                    gender=gender,
                    age=age,
                    address=address,
                    photo_path=self.photo_path
                )
                if success:
                    QMessageBox.information(self, "نجح", "تم تحديث بيانات المريض بنجاح")
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تحديث بيانات المريض")
            else:
                # إضافة مريض جديد
                patient_id = self.patient_model.add_patient(
                    file_number, full_name, phone, gender, age, address, self.photo_path
                )
                if patient_id:
                    QMessageBox.information(self, "نجح", "تم إضافة المريض بنجاح")
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إضافة المريض")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def select_photo(self):
        """اختيار صورة للمريض"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            from PyQt6.QtGui import QPixmap
            import os

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار صورة المريض",
                "",
                "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
            )

            if file_path:
                # التحقق من صحة الصورة
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    # تصغير الصورة للعرض
                    scaled_pixmap = pixmap.scaled(
                        120, 120,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )

                    self.photo_label.setPixmap(scaled_pixmap)
                    self.photo_path = file_path

                    # تفعيل أزرار الصورة
                    self.open_photo_button.setEnabled(True)
                    self.remove_photo_button.setEnabled(True)

                    # تحديث نص التسمية
                    self.photo_label.setText("")

                else:
                    QMessageBox.warning(self, "خطأ", "الملف المحدد ليس صورة صالحة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الصورة: {str(e)}")

    def open_photo(self):
        """فتح الصورة في نافذة منفصلة"""
        try:
            import os
            if self.photo_path and os.path.exists(self.photo_path):
                from PyQt6.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton
                from PyQt6.QtGui import QPixmap
                from PyQt6.QtCore import Qt

                # إنشاء نافذة عرض الصورة
                photo_dialog = QDialog(self)
                photo_dialog.setWindowTitle(f"صورة المريض - {self.full_name_input.text()}")
                photo_dialog.setModal(True)
                photo_dialog.resize(600, 600)

                layout = QVBoxLayout(photo_dialog)

                # عرض الصورة
                photo_label = QLabel()
                pixmap = QPixmap(self.photo_path)

                # تصغير الصورة للنافذة
                scaled_pixmap = pixmap.scaled(
                    550, 550,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )

                photo_label.setPixmap(scaled_pixmap)
                photo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                photo_label.setStyleSheet("""
                    QLabel {
                        border: 2px solid #3498db;
                        border-radius: 8px;
                        background-color: white;
                        padding: 10px;
                    }
                """)
                layout.addWidget(photo_label)

                # زر إغلاق
                close_button = QPushButton("إغلاق")
                close_button.setMinimumHeight(40)
                close_button.setStyleSheet("""
                    QPushButton {
                        background-color: #95a5a6;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        font-weight: bold;
                        font-size: 14px;
                        padding: 10px 20px;
                    }
                    QPushButton:hover {
                        background-color: #7f8c8d;
                    }
                """)
                close_button.clicked.connect(photo_dialog.close)
                layout.addWidget(close_button)

                photo_dialog.exec()

            else:
                QMessageBox.warning(self, "خطأ", "لا توجد صورة لعرضها أو الملف غير موجود")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الصورة: {str(e)}")

    def remove_photo(self):
        """حذف صورة المريض"""
        try:
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من حذف صورة المريض؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # إعادة تعيين الصورة
                self.photo_label.clear()
                self.photo_label.setText("لا توجد صورة")
                self.photo_path = None

                # تعطيل أزرار الصورة
                self.open_photo_button.setEnabled(False)
                self.remove_photo_button.setEnabled(False)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف الصورة: {str(e)}")

class PatientsWidget(QWidget):
    """ويدجت إدارة المرضى"""
    
    patient_selected = pyqtSignal(dict)  # إشارة اختيار مريض
    
    def __init__(self, patient_model, parent=None):
        super().__init__(parent)
        self.patient_model = patient_model
        self.current_patients = []
        self.style_manager = StyleManager()
        self.init_ui()
        self.load_patients()
    
    def init_ui(self):
        """إعداد واجهة المستخدم العصرية"""
        # تطبيق الأنماط العصرية
        combined_style = (
            self.style_manager.get_main_style() +
            self.style_manager.get_button_style() +
            self.style_manager.get_table_style() +
            self.style_manager.get_input_style() +
            self.style_manager.get_modern_card_style()
        )
        self.setStyleSheet(combined_style)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # شريط العنوان والبحث في بطاقة
        header_frame = QFrame()
        header_frame.setObjectName("cardFrame")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setSpacing(20)

        # العنوان
        title_label = QLabel("👥  إدارة المرضى")
        title_label.setObjectName("mainTitle")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # مربع البحث العصري
        search_container = QFrame()
        search_container.setStyleSheet("QFrame { border: none; background: transparent; }")
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(10)

        search_label = QLabel("🔍")
        search_label.setStyleSheet("color: #64748B; border: none; padding: 0; font-size: 16px;")
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في المرضى (الاسم، رقم الملف، الهاتف)...")
        self.search_input.setMinimumWidth(300)
        self.search_input.setMaximumWidth(400)
        self.search_input.setMinimumHeight(35)
        self.search_input.textChanged.connect(self.search_patients)
        search_layout.addWidget(self.search_input)

        header_layout.addWidget(search_container)

        # زر إضافة مريض عصري
        add_button = QPushButton("➕  إضافة مريض جديد")
        add_button.setObjectName("successButton")
        add_button.setMinimumHeight(40)
        add_button.setMinimumWidth(180)
        add_button.clicked.connect(self.add_patient)
        header_layout.addWidget(add_button)

        layout.addWidget(header_frame)
        
        # جدول المرضى في بطاقة عصرية
        table_frame = QFrame()
        table_frame.setObjectName("cardFrame")
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(20, 20, 20, 20)

        # عنوان الجدول
        table_title = QLabel("📋  قائمة المرضى")
        table_title.setObjectName("sectionTitle")
        table_layout.addWidget(table_title)

        self.patients_table = QTableWidget()
        self.patients_table.setColumnCount(6)
        self.patients_table.setHorizontalHeaderLabels([
            "رقم الملف", "الاسم الكامل", "الهاتف", "الجنس", "العمر", "العنوان"
        ])

        # تعديل عرض الأعمدة
        header = self.patients_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)

        # إعداد الجدول
        self.patients_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.patients_table.setAlternatingRowColors(True)
        self.patients_table.doubleClicked.connect(self.edit_patient)
        self.patients_table.itemSelectionChanged.connect(self.on_patient_selected)
        self.patients_table.setMinimumHeight(400)

        table_layout.addWidget(self.patients_table)
        layout.addWidget(table_frame)
        
        # أزرار التحكم العصرية
        buttons_frame = QFrame()
        buttons_frame.setObjectName("cardFrame")
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)

        edit_button = QPushButton("✏️  تعديل")
        edit_button.setObjectName("successButton")
        edit_button.setMinimumHeight(40)
        edit_button.setMinimumWidth(120)
        edit_button.clicked.connect(self.edit_patient)
        buttons_layout.addWidget(edit_button)

        delete_button = QPushButton("🗑️  حذف")
        delete_button.setObjectName("dangerButton")
        delete_button.setMinimumHeight(40)
        delete_button.setMinimumWidth(120)
        delete_button.clicked.connect(self.delete_patient)
        buttons_layout.addWidget(delete_button)

        view_visits_button = QPushButton("📋  عرض الزيارات")
        view_visits_button.setMinimumHeight(40)
        view_visits_button.setMinimumWidth(140)
        view_visits_button.clicked.connect(self.view_patient_visits)
        buttons_layout.addWidget(view_visits_button)

        buttons_layout.addStretch()

        refresh_button = QPushButton("🔄  تحديث")
        refresh_button.setObjectName("secondaryButton")
        refresh_button.setMinimumHeight(40)
        refresh_button.setMinimumWidth(120)
        refresh_button.clicked.connect(self.load_patients)
        buttons_layout.addWidget(refresh_button)

        layout.addWidget(buttons_frame)
    
    def load_patients(self):
        """تحميل قائمة المرضى"""
        try:
            self.current_patients = self.patient_model.search_patients()
            self.update_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المرضى: {str(e)}")
    
    def update_table(self):
        """تحديث جدول المرضى"""
        self.patients_table.setRowCount(len(self.current_patients))
        
        for row, patient in enumerate(self.current_patients):
            self.patients_table.setItem(row, 0, QTableWidgetItem(str(patient['file_number'])))
            self.patients_table.setItem(row, 1, QTableWidgetItem(patient['full_name']))
            self.patients_table.setItem(row, 2, QTableWidgetItem(patient['phone'] or ""))
            
            gender_text = ""
            if patient['gender'] == 'male':
                gender_text = "ذكر"
            elif patient['gender'] == 'female':
                gender_text = "أنثى"
            self.patients_table.setItem(row, 3, QTableWidgetItem(gender_text))
            
            self.patients_table.setItem(row, 4, QTableWidgetItem(str(patient['age']) if patient['age'] else ""))
            self.patients_table.setItem(row, 5, QTableWidgetItem(patient['address'] or ""))
    
    def search_patients(self):
        """البحث في المرضى"""
        search_term = self.search_input.text().strip()
        try:
            self.current_patients = self.patient_model.search_patients(search_term)
            self.update_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث: {str(e)}")
    
    def add_patient(self):
        """إضافة مريض جديد"""
        dialog = PatientDialog(self.patient_model, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_patients()
    
    def edit_patient(self):
        """تعديل مريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            patient_data = self.current_patients[current_row]
            dialog = PatientDialog(self.patient_model, patient_data, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_patients()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض للتعديل")
    
    def delete_patient(self):
        """حذف مريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            patient_data = self.current_patients[current_row]
            
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف المريض:\n{patient_data['full_name']}؟\n\nسيتم حذف جميع زياراته أيضاً!",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    success = self.patient_model.delete_patient(patient_data['id'])
                    if success:
                        QMessageBox.information(self, "نجح", "تم حذف المريض بنجاح")
                        self.load_patients()
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف المريض")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض للحذف")
    
    def view_patient_visits(self):
        """عرض زيارات المريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            patient_data = self.current_patients[current_row]
            self.patient_selected.emit(patient_data)
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض لعرض زياراته")
    
    def on_patient_selected(self):
        """التعامل مع اختيار مريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_patients):
            # يمكن إضافة منطق إضافي هنا عند اختيار مريض
            pass
