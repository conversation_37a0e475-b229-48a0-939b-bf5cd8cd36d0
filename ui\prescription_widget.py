from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QTextEdit, QPushButton, QMessageBox, QFrame,
                            QCompleter, QListWidget, QListWidgetItem, QSplitter,
                            QGroupBox, QLineEdit, QSpinBox, QComboBox)
from PyQt6.QtCore import Qt, pyqtSignal, QStringListModel, QTimer
from PyQt6.QtGui import QFont, QTextCursor, QTextCharFormat, QColor
import re

class MedicationCompleter(QCompleter):
    """مكمل تلقائي محسن للأدوية"""
    
    def __init__(self, medication_model, parent=None):
        super().__init__(parent)
        self.medication_model = medication_model
        self.setCompletionMode(QCompleter.CompletionMode.PopupCompletion)
        self.setCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.setMaxVisibleItems(10)
        self.update_medications()
    
    def update_medications(self):
        """تحديث قائمة الأدوية"""
        try:
            medications = self.medication_model.get_all_medication_names()
            model = QStringListModel(medications)
            self.setModel(model)
        except Exception as e:
            print(f"خطأ في تحديث الأدوية: {e}")

class SmartTextEdit(QTextEdit):
    """محرر نص ذكي مع اقتراحات الأدوية"""
    
    medication_added = pyqtSignal(str)  # إشارة إضافة دواء جديد
    
    def __init__(self, medication_model, parent=None):
        super().__init__(parent)
        self.medication_model = medication_model
        self.completer = MedicationCompleter(medication_model, self)
        self.completer.setWidget(self)
        self.completer.activated.connect(self.insert_completion)
        
        # تنسيق النص
        self.setStyleSheet("""
            QTextEdit {
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 14px;
                line-height: 1.6;
                padding: 15px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                background-color: white;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        
        # إعداد النص الافتراضي
        self.setPlaceholderText("""
اكتب وصفة العلاج هنا...

مثال:
1. باراسيتامول 500 مجم - قرص كل 8 ساعات
2. أموكسيسيلين 250 مجم - كبسولة كل 12 ساعة لمدة 7 أيام
3. فيتامين د 1000 وحدة - قرص يومياً

سيتم اقتراح أسماء الأدوية تلقائياً أثناء الكتابة...
        """.strip())
        
        # مؤقت للبحث المتأخر
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.show_suggestions)
        
        # ربط الأحداث
        self.textChanged.connect(self.on_text_changed)
    
    def keyPressEvent(self, event):
        """التعامل مع ضغط المفاتيح"""
        if self.completer.popup().isVisible():
            if event.key() in (Qt.Key.Key_Enter, Qt.Key.Key_Return, Qt.Key.Key_Escape, Qt.Key.Key_Tab, Qt.Key.Key_Backtab):
                event.ignore()
                return
        
        super().keyPressEvent(event)
        
        # إظهار الاقتراحات عند الكتابة
        if event.text() and event.text().isalnum():
            self.search_timer.start(300)  # تأخير 300 مللي ثانية
    
    def on_text_changed(self):
        """التعامل مع تغيير النص"""
        # استخراج الأدوية الجديدة وحفظها
        self.extract_and_save_medications()
    
    def show_suggestions(self):
        """إظهار اقتراحات الأدوية"""
        cursor = self.textCursor()
        cursor.select(QTextCursor.SelectionType.WordUnderCursor)
        word = cursor.selectedText().strip()
        
        if len(word) >= 2:  # إظهار الاقتراحات للكلمات من حرفين فأكثر
            self.completer.setCompletionPrefix(word)
            
            if self.completer.completionCount() > 0:
                rect = self.cursorRect()
                rect.setWidth(self.completer.popup().sizeHintForColumn(0) + 
                            self.completer.popup().verticalScrollBar().sizeHint().width())
                self.completer.complete(rect)
    
    def insert_completion(self, completion):
        """إدراج الاقتراح المحدد"""
        cursor = self.textCursor()
        cursor.select(QTextCursor.SelectionType.WordUnderCursor)
        cursor.insertText(completion)
        self.setTextCursor(cursor)
    
    def extract_and_save_medications(self):
        """استخراج الأدوية من النص وحفظها في قاعدة البيانات"""
        text = self.toPlainText()
        
        # نمط للبحث عن أسماء الأدوية (كلمات تبدأ بحرف كبير أو تحتوي على أرقام)
        medication_pattern = r'\b[A-Za-z][A-Za-z0-9\s]*(?:\d+\s*(?:مجم|mg|جم|g|مل|ml|وحدة|unit))?\b'
        
        medications = re.findall(medication_pattern, text)
        
        for medication in medications:
            medication = medication.strip()
            if len(medication) > 2 and not medication.isdigit():
                try:
                    # حفظ الدواء في قاعدة البيانات
                    self.medication_model.add_medication(medication)
                    self.medication_added.emit(medication)
                except:
                    pass  # تجاهل الأخطاء
        
        # تحديث قائمة الاقتراحات
        self.completer.update_medications()

class PrescriptionWidget(QWidget):
    """ويدجت وصف العلاج المتقدم"""
    
    def __init__(self, medication_model, visit_data=None, parent=None):
        super().__init__(parent)
        self.medication_model = medication_model
        self.visit_data = visit_data
        self.init_ui()
        
        if visit_data and visit_data.get('treatment_description'):
            self.prescription_text.setPlainText(visit_data['treatment_description'])
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                padding: 20px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        
        title_label = QLabel("💊 وصف العلاج الذكي")
        title_label.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white; border: none; padding: 0;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)
        
        subtitle_label = QLabel("اكتب الوصفة الطبية مع الاقتراحات التلقائية للأدوية")
        subtitle_label.setStyleSheet("color: rgba(255, 255, 255, 0.9); border: none; padding: 0; font-size: 14px;")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(subtitle_label)
        
        layout.addWidget(header_frame)
        
        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # الجانب الأيسر - محرر الوصفة
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # أدوات التنسيق
        tools_frame = QFrame()
        tools_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        tools_layout = QHBoxLayout(tools_frame)
        
        # أزرار التنسيق
        bold_button = QPushButton("🅱️ عريض")
        bold_button.setMinimumHeight(35)
        bold_button.clicked.connect(self.make_bold)
        tools_layout.addWidget(bold_button)
        
        italic_button = QPushButton("🅸 مائل")
        italic_button.setMinimumHeight(35)
        italic_button.clicked.connect(self.make_italic)
        tools_layout.addWidget(italic_button)
        
        underline_button = QPushButton("🅿️ تحته خط")
        underline_button.setMinimumHeight(35)
        underline_button.clicked.connect(self.make_underline)
        tools_layout.addWidget(underline_button)
        
        tools_layout.addStretch()
        
        # زر مسح النص
        clear_button = QPushButton("🗑️ مسح")
        clear_button.setMinimumHeight(35)
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        clear_button.clicked.connect(self.clear_text)
        tools_layout.addWidget(clear_button)
        
        left_layout.addWidget(tools_frame)
        
        # محرر النص الذكي
        prescription_group = QGroupBox("📝 نص الوصفة الطبية")
        prescription_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        prescription_layout = QVBoxLayout(prescription_group)
        
        self.prescription_text = SmartTextEdit(self.medication_model)
        self.prescription_text.setMinimumHeight(400)
        self.prescription_text.medication_added.connect(self.on_medication_added)
        prescription_layout.addWidget(self.prescription_text)
        
        left_layout.addWidget(prescription_group)
        
        # الجانب الأيمن - الأدوية والاقتراحات
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # قائمة الأدوية الشائعة
        popular_group = QGroupBox("⭐ الأدوية الشائعة")
        popular_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        popular_layout = QVBoxLayout(popular_group)
        
        # مربع البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("🔍")
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث في الأدوية...")
        self.search_input.setMinimumHeight(35)
        self.search_input.textChanged.connect(self.search_medications)
        search_layout.addWidget(self.search_input)
        
        popular_layout.addLayout(search_layout)
        
        # قائمة الأدوية
        self.medications_list = QListWidget()
        self.medications_list.setMaximumHeight(300)
        self.medications_list.itemDoubleClicked.connect(self.add_medication_to_prescription)
        popular_layout.addWidget(self.medications_list)
        
        right_layout.addWidget(popular_group)
        
        # قوالب الوصفات
        templates_group = QGroupBox("📋 قوالب جاهزة")
        templates_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        templates_layout = QVBoxLayout(templates_group)
        
        # أزرار القوالب
        common_cold_button = QPushButton("🤧 نزلة برد")
        common_cold_button.clicked.connect(lambda: self.insert_template("common_cold"))
        templates_layout.addWidget(common_cold_button)
        
        headache_button = QPushButton("🤕 صداع")
        headache_button.clicked.connect(lambda: self.insert_template("headache"))
        templates_layout.addWidget(headache_button)
        
        stomach_button = QPushButton("🤢 مشاكل معدة")
        stomach_button.clicked.connect(lambda: self.insert_template("stomach"))
        templates_layout.addWidget(stomach_button)
        
        right_layout.addWidget(templates_group)
        right_layout.addStretch()
        
        # إضافة الأجزاء للمقسم
        main_splitter.addWidget(left_widget)
        main_splitter.addWidget(right_widget)
        main_splitter.setSizes([700, 300])
        
        layout.addWidget(main_splitter)
        
        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        
        save_button = QPushButton("💾 حفظ الوصفة")
        save_button.setMinimumHeight(40)
        save_button.setMinimumWidth(150)
        save_button.clicked.connect(self.save_prescription)
        buttons_layout.addWidget(save_button)
        
        print_button = QPushButton("🖨️ طباعة الوصفة")
        print_button.setMinimumHeight(40)
        print_button.setMinimumWidth(150)
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        print_button.clicked.connect(self.print_prescription)
        buttons_layout.addWidget(print_button)
        
        buttons_layout.addStretch()
        
        preview_button = QPushButton("👁️ معاينة")
        preview_button.setMinimumHeight(40)
        preview_button.setMinimumWidth(120)
        preview_button.clicked.connect(self.preview_prescription)
        buttons_layout.addWidget(preview_button)
        
        layout.addWidget(buttons_frame)
        
        # تحميل الأدوية الشائعة
        self.load_popular_medications()
        
        # تطبيق الستايل العام
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QListWidget {
                border: 1px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f1f2f6;
                border-radius: 4px;
            }
            QListWidget::item:hover {
                background-color: #ecf0f1;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
    
    def load_popular_medications(self):
        """تحميل الأدوية الشائعة"""
        try:
            medications = self.medication_model.get_popular_medications(20)
            self.medications_list.clear()
            
            for medication in medications:
                item = QListWidgetItem(f"{medication['name']} ({medication['usage_count']} مرة)")
                item.setData(Qt.ItemDataRole.UserRole, medication['name'])
                self.medications_list.addItem(item)
        except Exception as e:
            print(f"خطأ في تحميل الأدوية: {e}")
    
    def search_medications(self):
        """البحث في الأدوية"""
        search_term = self.search_input.text().strip()
        try:
            if search_term:
                medications = self.medication_model.search_medications(search_term, 20)
            else:
                medications = self.medication_model.get_popular_medications(20)
            
            self.medications_list.clear()
            for medication in medications:
                item = QListWidgetItem(f"{medication['name']} ({medication['usage_count']} مرة)")
                item.setData(Qt.ItemDataRole.UserRole, medication['name'])
                self.medications_list.addItem(item)
        except Exception as e:
            print(f"خطأ في البحث: {e}")
    
    def add_medication_to_prescription(self, item):
        """إضافة دواء إلى الوصفة"""
        medication_name = item.data(Qt.ItemDataRole.UserRole)
        cursor = self.prescription_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        cursor.insertText(f"\n• {medication_name} - ")
        self.prescription_text.setTextCursor(cursor)
        self.prescription_text.setFocus()
    
    def insert_template(self, template_type):
        """إدراج قالب وصفة"""
        templates = {
            "common_cold": """
علاج نزلة البرد:
• باراسيتامول 500 مجم - قرص كل 8 ساعات عند الحاجة
• فيتامين سي 1000 مجم - قرص يومياً
• شراب السعال - ملعقة صغيرة 3 مرات يومياً
• راحة تامة وشرب السوائل الدافئة
            """.strip(),
            
            "headache": """
علاج الصداع:
• إيبوبروفين 400 مجم - قرص كل 6-8 ساعات عند الحاجة
• أو باراسيتامول 500 مجم - قرص كل 6 ساعات
• تجنب الضوضاء والأضواء الساطعة
• الراحة في مكان هادئ
            """.strip(),
            
            "stomach": """
علاج مشاكل المعدة:
• أوميبرازول 20 مجم - كبسولة قبل الإفطار بنصف ساعة
• سيميثيكون 40 مجم - قرص بعد الوجبات
• تجنب الأطعمة الحارة والدهنية
• تناول وجبات صغيرة ومتكررة
            """.strip()
        }
        
        template_text = templates.get(template_type, "")
        if template_text:
            cursor = self.prescription_text.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            cursor.insertText(f"\n\n{template_text}")
            self.prescription_text.setTextCursor(cursor)
    
    def make_bold(self):
        """جعل النص عريض"""
        cursor = self.prescription_text.textCursor()
        format = QTextCharFormat()
        format.setFontWeight(QFont.Weight.Bold if not cursor.charFormat().fontWeight() == QFont.Weight.Bold else QFont.Weight.Normal)
        cursor.mergeCharFormat(format)
    
    def make_italic(self):
        """جعل النص مائل"""
        cursor = self.prescription_text.textCursor()
        format = QTextCharFormat()
        format.setFontItalic(not cursor.charFormat().fontItalic())
        cursor.mergeCharFormat(format)
    
    def make_underline(self):
        """جعل النص تحته خط"""
        cursor = self.prescription_text.textCursor()
        format = QTextCharFormat()
        format.setFontUnderline(not cursor.charFormat().fontUnderline())
        cursor.mergeCharFormat(format)
    
    def clear_text(self):
        """مسح النص"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح نص الوصفة؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.prescription_text.clear()
    
    def on_medication_added(self, medication_name):
        """التعامل مع إضافة دواء جديد"""
        # تحديث قائمة الأدوية
        self.load_popular_medications()
    
    def save_prescription(self):
        """حفظ الوصفة"""
        prescription_text = self.prescription_text.toPlainText().strip()
        if not prescription_text:
            QMessageBox.warning(self, "تحذير", "لا يوجد نص للحفظ")
            return
        
        # هنا يمكن حفظ الوصفة في قاعدة البيانات
        QMessageBox.information(self, "تم الحفظ", "تم حفظ الوصفة بنجاح")
    
    def print_prescription(self):
        """طباعة الوصفة"""
        prescription_text = self.prescription_text.toPlainText().strip()
        if not prescription_text:
            QMessageBox.warning(self, "تحذير", "لا يوجد نص للطباعة")
            return
        
        # هنا يمكن إضافة منطق الطباعة
        QMessageBox.information(self, "طباعة", "سيتم تطوير نظام الطباعة قريباً")
    
    def preview_prescription(self):
        """معاينة الوصفة"""
        prescription_text = self.prescription_text.toPlainText().strip()
        if not prescription_text:
            QMessageBox.warning(self, "تحذير", "لا يوجد نص للمعاينة")
            return
        
        # عرض نافذة معاينة
        preview_dialog = QMessageBox(self)
        preview_dialog.setWindowTitle("معاينة الوصفة")
        preview_dialog.setText("معاينة الوصفة الطبية:")
        preview_dialog.setDetailedText(prescription_text)
        preview_dialog.exec()
